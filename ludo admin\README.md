# Ludo Admin Dashboard

A comprehensive admin dashboard for managing the Ludo game platform, built with Next.js, Supabase, and shadcn/ui.

## Features

- 🎮 **Game Management**: Monitor and manage game rooms, participants, and game history
- 👥 **User Management**: View, edit, block/unblock users, and manage user profiles
- 📊 **Analytics**: Comprehensive analytics with charts and metrics
- 🔐 **Authentication**: Secure admin authentication with role-based access
- 📝 **Audit Logs**: Track all administrative actions
- 💰 **Financial Tracking**: Monitor transactions, entry fees, and rewards
- 🎨 **Modern UI**: Beautiful, responsive interface with dark/light mode support

## Tech Stack

- **Frontend**: Next.js 13, React, TypeScript
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **UI Components**: shadcn/ui, Radix UI, Tailwind CSS
- **Charts**: Recharts
- **Icons**: Lucide React

## Database Schema

The application uses the following main tables:

- `users` - User profiles and game statistics
- `admin_users` - Admin user roles and permissions
- `game_rooms` - Game room information and status
- `game_participants` - Players in each game
- `audit_logs` - Administrative action tracking
- `transactions` - Financial transaction records
- `game_moves` - Game move history

## Setup Instructions

### 1. Prerequisites

- Node.js 18+ installed
- A Supabase project created at [supabase.com](https://supabase.com)

### 2. Environment Configuration

1. Copy the environment variables:
   ```bash
   cp .env.example .env
   ```

2. Update `.env` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

### 3. Database Setup

#### Option A: Manual Setup (Recommended)

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `supabase/migrations/20241205000001_initial_schema.sql`
4. Run the SQL to create tables
5. Copy and paste the contents of `supabase/migrations/20241205000002_rls_policies.sql`
6. Run the SQL to set up Row Level Security policies
7. Copy and paste the contents of `supabase/seed.sql`
8. Run the SQL to populate with sample data

#### Option B: Automated Setup (Experimental)

```bash
npm install
node scripts/setup-database.js
```

### 4. Install Dependencies

```bash
npm install
```

### 5. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

### 6. Create Admin Users

1. Go to your Supabase Auth dashboard
2. Create a new user with email/password
3. Copy the user ID
4. In the SQL Editor, run:
   ```sql
   INSERT INTO public.admin_users (id, email, role) 
   VALUES ('user_id_from_auth', '<EMAIL>', 'super_admin');
   ```

## Usage

### Admin Login

1. Navigate to `/auth/login`
2. Use the admin credentials you created
3. Access the dashboard at `/dashboard`

### Dashboard Features

- **Overview**: Key metrics and recent activity
- **User Management**: View and manage all users
- **Game Rooms**: Monitor active and completed games
- **Analytics**: Detailed charts and statistics
- **Security & Logs**: View audit logs and security events

## Development

### Project Structure

```
ludo admin/
├── app/                    # Next.js app directory
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── dashboard/         # Dashboard-specific components
│   └── ui/               # Reusable UI components
├── lib/                   # Utility libraries
│   ├── supabase/         # Supabase client configuration
│   └── utils/            # Helper functions
├── supabase/             # Database migrations and seeds
├── types/                # TypeScript type definitions
└── scripts/              # Setup and utility scripts
```

### Key Components

- `GameStatusCards`: Display key metrics
- `Overview`: Player activity charts
- `RecentActivity`: Recent platform events
- `Sidebar`: Navigation menu
- `UserAccountNav`: User profile dropdown

### Database Queries

All database interactions use the Supabase client with proper TypeScript types. Row Level Security (RLS) policies ensure data access control.

## Troubleshooting

### Common Issues

1. **Middleware Error**: If you see middleware errors, ensure `output: 'export'` is removed from `next.config.js`

2. **Database Connection**: Verify your Supabase URL and keys in the `.env` file

3. **Authentication Issues**: Ensure admin users are properly created in both Supabase Auth and the `admin_users` table

4. **Missing UI Components**: All shadcn/ui components are included. If you see import errors, check the component exists in `components/ui/`

### Getting Help

1. Check the browser console for error messages
2. Verify database setup in Supabase dashboard
3. Ensure all environment variables are set correctly
4. Check that the admin user exists in both auth.users and admin_users tables

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
