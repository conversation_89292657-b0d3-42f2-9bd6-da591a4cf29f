"use client";

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDistanceToNow } from 'date-fns';

interface UserProfile {
  id: string;
  username: string;
  avatar_url: string | null;
  created_at: string;
  games_played: number;
  games_won: number;
}

export function RecentUsers() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    const fetchRecentUsers = async () => {
      try {
        setIsLoading(true);

        const { data, error } = await supabase
          .from('user_profiles')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(5);

        if (error) throw error;

        setUsers(data || []);
      } catch (error) {
        console.error('Error fetching recent users:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecentUsers();

    // Set up subscription for real-time updates
    const subscription = supabase
      .channel('user_profiles_changes')
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'users' },
        () => fetchRecentUsers()
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [supabase]);

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {users.length === 0 ? (
        <p className="text-sm text-muted-foreground text-center py-4">No recent users found</p>
      ) : (
        users.map((user) => (
          <div key={user.id} className="flex items-center gap-3">
            <Avatar>
              <AvatarFallback>
                {user.username.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium leading-none">{user.username}</p>
              <p className="text-xs text-muted-foreground">
                Joined {formatDistanceToNow(new Date(user.created_at), { addSuffix: true })}
              </p>
            </div>
            <div className="ml-auto flex flex-col text-right">
              <span className="text-xs font-medium">Games: {user.games_played}</span>
              <span className="text-xs text-muted-foreground">Won: {user.games_won}</span>
            </div>
          </div>
        ))
      )}
    </div>
  );
}