import 'package:flame/components.dart';
import 'package:flame/effects.dart';
import 'package:flutter/material.dart';
import '../../models/game_participant_model.dart';
import '../../utils/app_theme.dart';

class PlayerHomeComponent extends RectangleComponent {
  final PlayerColor color;
  late TextComponent _piecesInStartText;
  late TextComponent _piecesFinishedText;
  late RectangleComponent _startArea;
  late RectangleComponent _finishArea;

  int _piecesInStart = 4;
  int _piecesFinished = 0;

  PlayerHomeComponent({
    required this.color,
    required Vector2 position,
    required Vector2 size,
  }) : super(
         position: position,
         size: size,
         paint: Paint()..color = _getPlayerColor(color).withValues(alpha: 0.1),
       );

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Create border
    final border = RectangleComponent(
      size: size,
      paint:
          Paint()
            ..color = _getPlayerColor(color)
            ..style = PaintingStyle.stroke
            ..strokeWidth = 3,
    );
    add(border);

    // Create start area indicator
    _startArea = RectangleComponent(
      position: Vector2(size.x * 0.1, size.y * 0.1),
      size: Vector2(size.x * 0.35, size.y * 0.8),
      paint: Paint()..color = _getPlayerColor(color).withValues(alpha: 0.3),
    );
    add(_startArea);

    // Create finish area indicator
    _finishArea = RectangleComponent(
      position: Vector2(size.x * 0.55, size.y * 0.1),
      size: Vector2(size.x * 0.35, size.y * 0.8),
      paint: Paint()..color = const Color(0xFFFFD700).withValues(alpha: 0.3),
    );
    add(_finishArea);

    // Create text for pieces in start
    _piecesInStartText = TextComponent(
      text: '$_piecesInStart',
      textRenderer: TextPaint(
        style: TextStyle(
          color: _getPlayerColor(color),
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
      anchor: Anchor.center,
    );
    _piecesInStartText.position = Vector2(
      _startArea.position.x + _startArea.size.x / 2,
      _startArea.position.y + _startArea.size.y / 2,
    );
    add(_piecesInStartText);

    // Create text for pieces finished
    _piecesFinishedText = TextComponent(
      text: '$_piecesFinished',
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Color(0xFFFFD700),
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
      anchor: Anchor.center,
    );
    _piecesFinishedText.position = Vector2(
      _finishArea.position.x + _finishArea.size.x / 2,
      _finishArea.position.y + _finishArea.size.y / 2,
    );
    add(_piecesFinishedText);

    // Add labels
    final startLabel = TextComponent(
      text: 'START',
      textRenderer: TextPaint(
        style: TextStyle(
          color: _getPlayerColor(color),
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
      anchor: Anchor.center,
    );
    startLabel.position = Vector2(
      _startArea.position.x + _startArea.size.x / 2,
      _startArea.position.y - 15,
    );
    add(startLabel);

    final finishLabel = TextComponent(
      text: 'FINISH',
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Color(0xFFFFD700),
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
      anchor: Anchor.center,
    );
    finishLabel.position = Vector2(
      _finishArea.position.x + _finishArea.size.x / 2,
      _finishArea.position.y - 15,
    );
    add(finishLabel);

    // Add player color indicator
    final colorIndicator = CircleComponent(
      radius: 15,
      paint: Paint()..color = _getPlayerColor(color),
      position: Vector2(size.x / 2 - 15, size.y - 35),
    );
    add(colorIndicator);

    final colorBorder = CircleComponent(
      radius: 15,
      paint:
          Paint()
            ..color = Colors.white
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2,
      position: Vector2(size.x / 2 - 15, size.y - 35),
    );
    add(colorBorder);
  }

  void updatePieceCounts(int piecesInStart, int piecesFinished) {
    if (_piecesInStart != piecesInStart) {
      _piecesInStart = piecesInStart;
      _piecesInStartText.text = '$_piecesInStart';

      // Add animation when count changes
      _animateCountChange(_piecesInStartText);
    }

    if (_piecesFinished != piecesFinished) {
      _piecesFinished = piecesFinished;
      _piecesFinishedText.text = '$_piecesFinished';

      // Add animation when count changes
      _animateCountChange(_piecesFinishedText);

      // Special animation for finishing pieces
      if (piecesFinished > _piecesFinished) {
        _animateFinishCelebration();
      }
    }
  }

  void _animateCountChange(TextComponent textComponent) {
    // Scale animation for count change
    final scaleEffect = ScaleEffect.to(
      Vector2.all(1.3),
      EffectController(
        duration: 0.2,
        alternate: true,
        curve: Curves.elasticOut,
      ),
    );
    textComponent.add(scaleEffect);
  }

  void _animateFinishCelebration() {
    // Celebration animation when a piece finishes
    final glowEffect = ColorEffect(
      const Color(0xFFFFD700).withValues(alpha: 0.8),
      EffectController(duration: 0.5, alternate: true, repeatCount: 2),
    );
    _finishArea.add(glowEffect);

    // Pulse effect
    final pulseEffect = ScaleEffect.to(
      Vector2.all(1.1),
      EffectController(duration: 0.3, alternate: true, curve: Curves.easeInOut),
    );
    _finishArea.add(pulseEffect);
  }

  void setActive(bool isActive) {
    if (isActive) {
      // Highlight this player's home when it's their turn
      paint.color = _getPlayerColor(color).withValues(alpha: 0.3);

      // Add pulsing border effect
      final borderPulse = ScaleEffect.to(
        Vector2.all(1.02),
        EffectController(
          duration: 1.0,
          alternate: true,
          infinite: true,
          curve: Curves.easeInOut,
        ),
      );
      add(borderPulse);
    } else {
      // Reset to normal appearance
      paint.color = _getPlayerColor(color).withValues(alpha: 0.1);

      // Remove pulsing effects
      removeAll(children.whereType<ScaleEffect>());
    }
  }

  static Color _getPlayerColor(PlayerColor color) {
    switch (color) {
      case PlayerColor.red:
        return AppTheme.primaryRed;
      case PlayerColor.blue:
        return AppTheme.primaryBlue;
      case PlayerColor.green:
        return AppTheme.primaryGreen;
      case PlayerColor.yellow:
        return AppTheme.primaryYellow;
    }
  }
}
