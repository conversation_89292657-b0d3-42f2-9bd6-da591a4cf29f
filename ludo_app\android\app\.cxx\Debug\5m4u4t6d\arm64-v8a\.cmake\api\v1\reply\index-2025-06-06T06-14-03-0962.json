{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/android/sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/android/sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/android/sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/android/sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-263ded13b2ca89d5060a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-58c9ed1373752fb7b9ae.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-bf164f23212ccb123b1f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-58c9ed1373752fb7b9ae.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-bf164f23212ccb123b1f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-263ded13b2ca89d5060a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}