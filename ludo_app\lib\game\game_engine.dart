import 'dart:math';
import '../models/game_state_model.dart';
import '../models/game_participant_model.dart';
// import '../models/game_move_model.dart';

class GameEngine {
  static const int boardSize = 52;
  static const int homeStretch = 6;
  static final Random _random = Random();

  // Starting positions for each color
  static const Map<PlayerColor, int> startingPositions = {
    PlayerColor.red: 1,
    PlayerColor.blue: 14,
    PlayerColor.green: 27,
    PlayerColor.yellow: 40,
  };

  // Safe positions on the board
  static const Set<int> safePositions = {1, 9, 14, 22, 27, 35, 40, 48};

  // Roll dice
  static int rollDice() {
    return _random.nextInt(6) + 1;
  }

  // Check if a piece can move
  static bool canPieceMove(
    GamePiece piece,
    int diceValue,
    GameStateModel gameState,
  ) {
    // If piece is in starting area
    if (piece.position == -1) {
      return diceValue == 6; // Can only move out with a 6
    }

    // If piece has reached the end
    if (piece.hasReachedEnd) {
      return false;
    }

    // Calculate new position
    final newPosition = calculateNewPosition(piece, diceValue);

    // Check if the move is valid
    return isValidMove(piece, newPosition, gameState);
  }

  // Calculate new position after move
  static int calculateNewPosition(GamePiece piece, int diceValue) {
    if (piece.position == -1) {
      // Moving out of starting area
      return startingPositions[piece.color]!;
    }

    int newPosition = piece.position + diceValue;

    // Handle entering home column
    final homeEntryPosition = _getHomeEntryPosition(piece.color);

    // Check if piece has completed a full lap and should enter home column
    if (piece.position <= homeEntryPosition &&
        newPosition > homeEntryPosition) {
      // Calculate how many steps into home column
      final stepsIntoHome = newPosition - homeEntryPosition - 1;
      if (stepsIntoHome < homeStretch) {
        // Enter home column (use negative positions for home column)
        return -(stepsIntoHome + 1);
      } else {
        // Can't move - would overshoot home
        return piece.position; // Invalid move
      }
    }

    // Handle movement within home column
    if (piece.position < 0) {
      final currentHomePos = -piece.position;
      final newHomePos = currentHomePos + diceValue;
      if (newHomePos <= homeStretch) {
        return -newHomePos;
      } else {
        // Can't move - would overshoot home
        return piece.position; // Invalid move
      }
    }

    // Handle normal board movement with wrapping
    if (newPosition >= boardSize) {
      newPosition = newPosition - boardSize;
    }

    return newPosition;
  }

  // Get home entry position for each color
  static int _getHomeEntryPosition(PlayerColor color) {
    switch (color) {
      case PlayerColor.red:
        return 50; // Position 50 leads to red home
      case PlayerColor.blue:
        return 11; // Position 11 leads to blue home
      case PlayerColor.green:
        return 24; // Position 24 leads to green home
      case PlayerColor.yellow:
        return 37; // Position 37 leads to yellow home
    }
  }

  // Check if a move is valid
  static bool isValidMove(
    GamePiece piece,
    int newPosition,
    GameStateModel gameState,
  ) {
    // If new position is same as current position, it's an invalid move
    if (newPosition == piece.position) {
      return false;
    }

    // Check if the new position is occupied by own piece
    final currentPlayer = gameState.players.firstWhere(
      (p) => p.color == piece.color,
    );

    for (final otherPiece in currentPlayer.pieces) {
      if (otherPiece.id != piece.id && otherPiece.position == newPosition) {
        return false; // Can't move to position occupied by own piece
      }
    }

    return true;
  }

  // Execute a move
  static GameStateModel executeMove(
    GameStateModel gameState,
    int pieceId,
    int diceValue,
  ) {
    final currentPlayer = gameState.currentPlayer;
    final piece = currentPlayer.pieces.firstWhere((p) => p.id == pieceId);

    if (!canPieceMove(piece, diceValue, gameState)) {
      throw Exception('Invalid move');
    }

    // Calculate new position
    final newPosition = calculateNewPosition(piece, diceValue);

    // Check for captures
    final capturedPieces = <GamePiece>[];
    for (final player in gameState.players) {
      if (player.color != currentPlayer.color) {
        for (final otherPiece in player.pieces) {
          if (otherPiece.position == newPosition &&
              !safePositions.contains(newPosition)) {
            capturedPieces.add(otherPiece);
          }
        }
      }
    }

    // Check if piece has reached the end (home position 6)
    final hasReachedEnd = newPosition == -homeStretch;

    // Update piece position
    final updatedPiece = piece.copyWith(
      position: newPosition,
      hasReachedEnd: hasReachedEnd,
    );

    // Update pieces list
    final updatedPieces =
        currentPlayer.pieces.map((p) {
          if (p.id == pieceId) return updatedPiece;
          return p;
        }).toList();

    // Update current player
    final updatedCurrentPlayer = currentPlayer.copyWith(pieces: updatedPieces);

    // Update all players
    final updatedPlayers =
        gameState.players.map((player) {
          if (player.color == currentPlayer.color) {
            return updatedCurrentPlayer;
          }

          // Handle captured pieces
          if (capturedPieces.any((cp) => cp.color == player.color)) {
            final updatedPlayerPieces =
                player.pieces.map((p) {
                  if (capturedPieces.any(
                    (cp) => cp.id == p.id && cp.color == p.color,
                  )) {
                    return p.copyWith(
                      position: -1,
                    ); // Send back to starting area
                  }
                  return p;
                }).toList();

            return player.copyWith(pieces: updatedPlayerPieces);
          }

          return player;
        }).toList();

    // Check if player gets another turn (rolled 6 or captured a piece)
    final getsAnotherTurn = diceValue == 6 || capturedPieces.isNotEmpty;

    // Calculate next player index
    int nextPlayerIndex = gameState.currentPlayerIndex;
    if (!getsAnotherTurn) {
      nextPlayerIndex =
          (gameState.currentPlayerIndex + 1) % gameState.players.length;
    }

    // Check for win condition
    final hasWon = updatedCurrentPlayer.pieces.every((p) => p.hasReachedEnd);

    return gameState.copyWith(
      players: updatedPlayers,
      currentPlayerIndex: nextPlayerIndex,
      lastDiceValue: diceValue,
      gameEnded: hasWon,
      winnerId: hasWon ? currentPlayer.id : null,
      lastMoveTime: DateTime.now(),
    );
  }

  // Get available moves for current player
  static List<int> getAvailableMoves(GameStateModel gameState, int diceValue) {
    final currentPlayer = gameState.currentPlayer;
    final availablePieces = <int>[];

    for (final piece in currentPlayer.pieces) {
      if (canPieceMove(piece, diceValue, gameState)) {
        availablePieces.add(piece.id);
      }
    }

    return availablePieces;
  }

  // Check if game is over
  static bool isGameOver(GameStateModel gameState) {
    return gameState.players.any(
      (player) => player.pieces.every((piece) => piece.hasReachedEnd),
    );
  }

  // Get winner
  static Player? getWinner(GameStateModel gameState) {
    for (final player in gameState.players) {
      if (player.pieces.every((piece) => piece.hasReachedEnd)) {
        return player;
      }
    }
    return null;
  }

  // Calculate score for a player
  static int calculateScore(Player player) {
    int score = 0;
    for (final piece in player.pieces) {
      if (piece.hasReachedEnd) {
        score += 100; // Bonus for reaching end
      } else if (piece.position > -1) {
        score += piece.position; // Points for progress
      }
    }
    return score;
  }
}
