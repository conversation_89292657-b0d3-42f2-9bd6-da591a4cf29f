"use client";

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/lib/supabase/client';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useTheme } from 'next-themes';
import { DownloadIcon, RefreshCwIcon, FilterIcon } from 'lucide-react';
import { addDays, format, subDays, subMonths } from 'date-fns';

interface AnalyticsData {
  gamesPerDay: Array<{
    date: string;
    standard: number;
    quick: number;
    tournament: number;
  }>;
  userRegistrations: Array<{
    date: string;
    count: number;
  }>;
  gameOutcomes: Array<{
    name: string;
    value: number;
  }>;
  revenueData: Array<{
    date: string;
    amount: number;
  }>;
  userEngagement: Array<{
    date: string;
    activeUsers: number;
    newUsers: number;
  }>;
}

const INITIAL_DATA: AnalyticsData = {
  gamesPerDay: [],
  userRegistrations: [],
  gameOutcomes: [],
  revenueData: [],
  userEngagement: []
};

export default function AnalyticsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>(INITIAL_DATA);
  const { toast } = useToast();
  const { theme } = useTheme();
  const supabase = createClient();

  // Get theme-specific colors
  const getChartColors = () => {
    return {
      primary: 'hsl(var(--chart-1))',
      secondary: 'hsl(var(--chart-2))',
      tertiary: 'hsl(var(--chart-3))',
      quaternary: 'hsl(var(--chart-4))',
      quinary: 'hsl(var(--chart-5))',
      grid: theme === 'dark' ? 'hsl(var(--border))' : 'hsl(var(--border))',
      text: theme === 'dark' ? 'hsl(var(--muted-foreground))' : 'hsl(var(--muted-foreground))'
    };
  };

  const colors = getChartColors();

  const fetchAnalyticsData = useCallback(async () => {
    try {
      setIsLoading(true);

      // Calculate date range based on selected time range
      const endDate = new Date();
      let startDate: Date;

      switch (timeRange) {
        case '7d':
          startDate = subDays(endDate, 7);
          break;
        case '30d':
          startDate = subDays(endDate, 30);
          break;
        case '90d':
          startDate = subDays(endDate, 90);
          break;
        default:
          startDate = subDays(endDate, 30);
      }

      const startDateIso = startDate.toISOString();

      // Fetch games per day
      const { data: gamesData, error: gamesError } = await supabase
        .from('game_rooms')
        .select('created_at, game_type, status')
        .gte('created_at', startDateIso);

      if (gamesError) throw gamesError;

      // Fetch user registrations
      const { data: usersData, error: usersError } = await supabase
        .from('user_profiles')
        .select('created_at')
        .gte('created_at', startDateIso);

      if (usersError) throw usersError;

      // Process games per day data
      const gamesByDay = processGamesByDay(gamesData || [], startDate, endDate);

      // Process user registrations data
      const usersByDay = processUsersByDay(usersData || [], startDate, endDate);

      // Process game outcomes data
      const outcomesData = processGameOutcomes(gamesData || []);

      // Mock revenue data for now (would be replaced with real data in production)
      const revenueData = generateMockRevenueData(startDate, endDate);

      // Mock user engagement data
      const engagementData = generateMockEngagementData(startDate, endDate);

      setAnalyticsData({
        gamesPerDay: gamesByDay,
        userRegistrations: usersByDay,
        gameOutcomes: outcomesData,
        revenueData: revenueData,
        userEngagement: engagementData
      });
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      toast({
        title: "Error loading analytics data",
        description: "Please try refreshing the page",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [supabase, timeRange, toast]);

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange, supabase, fetchAnalyticsData]);

  const processGamesByDay = (games: any[], startDate: Date, endDate: Date) => {
    const days: { [key: string]: { standard: number; quick: number; tournament: number } } = {};

    // Initialize all days in range
    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dateKey = format(currentDate, 'MMM dd');
      days[dateKey] = { standard: 0, quick: 0, tournament: 0 };
      currentDate = addDays(currentDate, 1);
    }

    // Count games by day and type
    games.forEach(game => {
      const gameDate = new Date(game.created_at);
      const dateKey = format(gameDate, 'MMM dd');

      if (days[dateKey]) {
        if (game.game_type === 'standard') {
          days[dateKey].standard += 1;
        } else if (game.game_type === 'quick') {
          days[dateKey].quick += 1;
        } else if (game.game_type === 'tournament') {
          days[dateKey].tournament += 1;
        }
      }
    });

    // Convert to array format for charts
    const result: Array<{ date: string; standard: number; quick: number; tournament: number }> =
      Object.entries(days).map(([date, counts]) => ({
        date,
        standard: counts.standard,
        quick: counts.quick,
        tournament: counts.tournament
      }));

    return result;
  };

  const processUsersByDay = (users: any[], startDate: Date, endDate: Date) => {
    const days: { [key: string]: number } = {};

    // Initialize all days in range
    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dateKey = format(currentDate, 'MMM dd');
      days[dateKey] = 0;
      currentDate = addDays(currentDate, 1);
    }

    // Count user registrations by day
    users.forEach(user => {
      const userDate = new Date(user.created_at);
      const dateKey = format(userDate, 'MMM dd');

      if (days[dateKey] !== undefined) {
        days[dateKey] += 1;
      }
    });

    // Convert to array format for charts
    return Object.entries(days).map(([date, count]) => ({
      date,
      count
    }));
  };

  const processGameOutcomes = (games: any[]) => {
    const outcomes = {
      completed: 0,
      active: 0,
      waiting: 0,
      paused: 0
    };

    games.forEach(game => {
      if (game.status === 'completed') {
        outcomes.completed += 1;
      } else if (game.status === 'active') {
        outcomes.active += 1;
      } else if (game.status === 'waiting') {
        outcomes.waiting += 1;
      } else if (game.status === 'paused') {
        outcomes.paused += 1;
      }
    });

    return [
      { name: 'Completed', value: outcomes.completed },
      { name: 'Active', value: outcomes.active },
      { name: 'Waiting', value: outcomes.waiting },
      { name: 'Paused', value: outcomes.paused }
    ];
  };

  const generateMockRevenueData = (startDate: Date, endDate: Date) => {
    const result = [];
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      result.push({
        date: format(currentDate, 'MMM dd'),
        amount: Math.floor(Math.random() * 500) + 100
      });
      currentDate = addDays(currentDate, 1);
    }

    return result;
  };

  const generateMockEngagementData = (startDate: Date, endDate: Date) => {
    const result = [];
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      result.push({
        date: format(currentDate, 'MMM dd'),
        activeUsers: Math.floor(Math.random() * 100) + 50,
        newUsers: Math.floor(Math.random() * 20) + 5
      });
      currentDate = addDays(currentDate, 1);
    }

    return result;
  };

  const handleRefresh = () => {
    fetchAnalyticsData();
    toast({
      title: "Refreshing data",
      description: "Analytics data is being updated"
    });
  };

  const handleExport = () => {
    toast({
      title: "Export initiated",
      description: "Analytics data export started"
    });

    // Mock export functionality
    setTimeout(() => {
      toast({
        title: "Export completed",
        description: "Analytics data has been exported successfully"
      });
    }, 1500);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">
            Comprehensive analytics and insights for your Ludo platform
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as '7d' | '30d' | '90d')}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Select timeframe" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="icon" onClick={handleRefresh}>
            <RefreshCwIcon className="h-4 w-4" />
          </Button>

          <Button variant="outline" onClick={handleExport} className="flex items-center gap-2">
            <DownloadIcon className="h-4 w-4" />
            <span className="hidden sm:inline">Export</span>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="games">Games</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Total Games</CardTitle>
                <CardDescription>All game rooms created</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <div className="text-3xl font-bold">
                    {analyticsData.gameOutcomes.reduce((sum, item) => sum + item.value, 0).toLocaleString()}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Active Users</CardTitle>
                <CardDescription>Users active this period</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <div className="text-3xl font-bold">
                    {(Math.floor(Math.random() * 1000) + 500).toLocaleString()}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Total Revenue</CardTitle>
                <CardDescription>Entry fees collected</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <div className="text-3xl font-bold">
                    ${analyticsData.revenueData.reduce((sum, item) => sum + item.amount, 0).toLocaleString()}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Game Distribution</CardTitle>
                <CardDescription>
                  Distribution of games by status
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center h-80">
                    <Skeleton className="h-64 w-64 rounded-full" />
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={analyticsData.gameOutcomes}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {analyticsData.gameOutcomes.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={[colors.primary, colors.secondary, colors.tertiary, colors.quaternary][index % 4]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>User Engagement</CardTitle>
                <CardDescription>
                  Active and new users over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[300px] w-full" />
                ) : (
                  <ResponsiveContainer width="100%\" height={300}>
                    <LineChart
                      data={analyticsData.userEngagement}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
                      <XAxis
                        dataKey="date"
                        tick={{ fill: colors.text, fontSize: 12 }}
                        tickLine={{ stroke: colors.grid }}
                        axisLine={{ stroke: colors.grid }}
                      />
                      <YAxis
                        tick={{ fill: colors.text, fontSize: 12 }}
                        tickLine={{ stroke: colors.grid }}
                        axisLine={{ stroke: colors.grid }}
                      />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="activeUsers"
                        stroke={colors.primary}
                        name="Active Users"
                        activeDot={{ r: 8 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="newUsers"
                        stroke={colors.secondary}
                        name="New Users"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="games" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Games Created</CardTitle>
              <CardDescription>
                Number of games created per day by type
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-[400px] w-full" />
              ) : (
                <ResponsiveContainer width="100%\" height={400}>
                  <BarChart
                    data={analyticsData.gamesPerDay}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 60,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
                    <XAxis
                      dataKey="date"
                      tick={{ fill: colors.text, fontSize: 12 }}
                      tickLine={{ stroke: colors.grid }}
                      axisLine={{ stroke: colors.grid }}
                      angle={-45}
                      textAnchor="end"
                      height={50}
                    />
                    <YAxis
                      tick={{ fill: colors.text, fontSize: 12 }}
                      tickLine={{ stroke: colors.grid }}
                      axisLine={{ stroke: colors.grid }}
                    />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="standard" name="Standard Games" fill={colors.primary} />
                    <Bar dataKey="quick" name="Quick Games" fill={colors.secondary} />
                    <Bar dataKey="tournament" name="Tournament Games" fill={colors.tertiary} />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Popular Game Times</CardTitle>
                <CardDescription>
                  When users play the most games
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                {isLoading ? (
                  <Skeleton className="h-[200px] w-full" />
                ) : (
                  <div className="space-y-8">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium">Morning (6AM-12PM)</div>
                        <div className="text-sm text-muted-foreground">28%</div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted overflow-hidden">
                        <div className="h-full bg-primary rounded-full" style={{ width: '28%' }} />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium">Afternoon (12PM-6PM)</div>
                        <div className="text-sm text-muted-foreground">35%</div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted overflow-hidden">
                        <div className="h-full bg-primary rounded-full" style={{ width: '35%' }} />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium">Evening (6PM-12AM)</div>
                        <div className="text-sm text-muted-foreground">52%</div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted overflow-hidden">
                        <div className="h-full bg-primary rounded-full" style={{ width: '52%' }} />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium">Night (12AM-6AM)</div>
                        <div className="text-sm text-muted-foreground">15%</div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted overflow-hidden">
                        <div className="h-full bg-primary rounded-full" style={{ width: '15%' }} />
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Game Completion Rate</CardTitle>
                <CardDescription>
                  Percentage of games that reach completion
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center justify-center h-[250px]">
                {isLoading ? (
                  <Skeleton className="h-40 w-40 rounded-full" />
                ) : (
                  <div className="relative flex items-center justify-center">
                    <svg className="w-40 h-40">
                      <circle
                        className="text-muted stroke-current"
                        strokeWidth="8"
                        stroke="currentColor"
                        fill="transparent"
                        r="62"
                        cx="80"
                        cy="80"
                      />
                      <circle
                        className="text-primary stroke-current"
                        strokeWidth="8"
                        strokeLinecap="round"
                        stroke="currentColor"
                        fill="transparent"
                        r="62"
                        cx="80"
                        cy="80"
                        strokeDasharray={62 * 2 * Math.PI}
                        strokeDashoffset={62 * 2 * Math.PI * (1 - 0.78)}
                        style={{
                          transformOrigin: "80px 80px",
                          transform: "rotate(-90deg)",
                        }}
                      />
                    </svg>
                    <div className="absolute flex flex-col items-center justify-center text-center">
                      <span className="text-4xl font-bold">78%</span>
                      <span className="text-sm text-muted-foreground">Completion Rate</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>New User Registrations</CardTitle>
              <CardDescription>
                New users registered over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-[400px] w-full" />
              ) : (
                <ResponsiveContainer width="100%\" height={400}>
                  <LineChart
                    data={analyticsData.userRegistrations}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 60,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
                    <XAxis
                      dataKey="date"
                      tick={{ fill: colors.text, fontSize: 12 }}
                      tickLine={{ stroke: colors.grid }}
                      axisLine={{ stroke: colors.grid }}
                      angle={-45}
                      textAnchor="end"
                      height={50}
                    />
                    <YAxis
                      tick={{ fill: colors.text, fontSize: 12 }}
                      tickLine={{ stroke: colors.grid }}
                      axisLine={{ stroke: colors.grid }}
                    />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="count"
                      name="New Users"
                      stroke={colors.primary}
                      activeDot={{ r: 8 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>User Demographics</CardTitle>
                <CardDescription>
                  Breakdown of user demographics
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                {isLoading ? (
                  <Skeleton className="h-[200px] w-full" />
                ) : (
                  <div className="space-y-8">
                    <div>
                      <div className="mb-2 flex items-center justify-between">
                        <div className="text-sm font-medium">Age Groups</div>
                      </div>
                      <div className="flex items-start">
                        <div className="flex-1 space-y-2">
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <div className="text-xs">18-24</div>
                              <div className="text-xs text-muted-foreground">32%</div>
                            </div>
                            <div className="h-1.5 w-full rounded-full bg-muted overflow-hidden">
                              <div className="h-full bg-primary rounded-full" style={{ width: '32%' }} />
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <div className="text-xs">25-34</div>
                              <div className="text-xs text-muted-foreground">45%</div>
                            </div>
                            <div className="h-1.5 w-full rounded-full bg-muted overflow-hidden">
                              <div className="h-full bg-primary rounded-full" style={{ width: '45%' }} />
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <div className="text-xs">35-44</div>
                              <div className="text-xs text-muted-foreground">15%</div>
                            </div>
                            <div className="h-1.5 w-full rounded-full bg-muted overflow-hidden">
                              <div className="h-full bg-primary rounded-full" style={{ width: '15%' }} />
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <div className="text-xs">45+</div>
                              <div className="text-xs text-muted-foreground">8%</div>
                            </div>
                            <div className="h-1.5 w-full rounded-full bg-muted overflow-hidden">
                              <div className="h-full bg-primary rounded-full" style={{ width: '8%' }} />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <div className="mb-2 flex items-center justify-between">
                        <div className="text-sm font-medium">Regions</div>
                      </div>
                      <div className="flex items-start">
                        <div className="flex-1 space-y-2">
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <div className="text-xs">North America</div>
                              <div className="text-xs text-muted-foreground">38%</div>
                            </div>
                            <div className="h-1.5 w-full rounded-full bg-muted overflow-hidden">
                              <div className="h-full bg-secondary rounded-full" style={{ width: '38%' }} />
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <div className="text-xs">Europe</div>
                              <div className="text-xs text-muted-foreground">27%</div>
                            </div>
                            <div className="h-1.5 w-full rounded-full bg-muted overflow-hidden">
                              <div className="h-full bg-secondary rounded-full" style={{ width: '27%' }} />
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <div className="text-xs">Asia</div>
                              <div className="text-xs text-muted-foreground">24%</div>
                            </div>
                            <div className="h-1.5 w-full rounded-full bg-muted overflow-hidden">
                              <div className="h-full bg-secondary rounded-full" style={{ width: '24%' }} />
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <div className="text-xs">Other</div>
                              <div className="text-xs text-muted-foreground">11%</div>
                            </div>
                            <div className="h-1.5 w-full rounded-full bg-muted overflow-hidden">
                              <div className="h-full bg-secondary rounded-full" style={{ width: '11%' }} />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Player Retention</CardTitle>
                <CardDescription>
                  User retention by time period
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[200px] w-full" />
                ) : (
                  <ResponsiveContainer width="100%\" height={250}>
                    <BarChart
                      data={[
                        { period: 'Day 1', retention: 100 },
                        { period: 'Day 7', retention: 65 },
                        { period: 'Day 30', retention: 42 },
                        { period: 'Day 90', retention: 28 },
                      ]}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
                      <XAxis
                        dataKey="period"
                        tick={{ fill: colors.text, fontSize: 12 }}
                        tickLine={{ stroke: colors.grid }}
                        axisLine={{ stroke: colors.grid }}
                      />
                      <YAxis
                        tick={{ fill: colors.text, fontSize: 12 }}
                        tickLine={{ stroke: colors.grid }}
                        axisLine={{ stroke: colors.grid }}
                        domain={[0, 100]}
                        unit="%"
                      />
                      <Tooltip />
                      <Bar dataKey="retention" name="Retention Rate" fill={colors.tertiary} radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Over Time</CardTitle>
              <CardDescription>
                Daily revenue from entry fees
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-[400px] w-full" />
              ) : (
                <ResponsiveContainer width="100%\" height={400}>
                  <LineChart
                    data={analyticsData.revenueData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 60,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
                    <XAxis
                      dataKey="date"
                      tick={{ fill: colors.text, fontSize: 12 }}
                      tickLine={{ stroke: colors.grid }}
                      axisLine={{ stroke: colors.grid }}
                      angle={-45}
                      textAnchor="end"
                      height={50}
                    />
                    <YAxis
                      tick={{ fill: colors.text, fontSize: 12 }}
                      tickLine={{ stroke: colors.grid }}
                      axisLine={{ stroke: colors.grid }}
                    />
                    <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="amount"
                      name="Revenue ($)"
                      stroke={colors.quaternary}
                      activeDot={{ r: 8 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue by Game Type</CardTitle>
                <CardDescription>
                  Distribution of revenue by game type
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[250px] w-full" />
                ) : (
                  <ResponsiveContainer width="100%\" height={250}>
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'Standard', value: 4200 },
                          { name: 'Quick', value: 3100 },
                          { name: 'Tournament', value: 9800 },
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        <Cell fill={colors.primary} />
                        <Cell fill={colors.secondary} />
                        <Cell fill={colors.tertiary} />
                      </Pie>
                      <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Average Revenue per User</CardTitle>
                <CardDescription>
                  ARPU over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[250px] w-full" />
                ) : (
                  <ResponsiveContainer width="100%\" height={250}>
                    <BarChart
                      data={[
                        { month: 'Jan', arpu: 12.45 },
                        { month: 'Feb', arpu: 14.20 },
                        { month: 'Mar', arpu: 15.80 },
                        { month: 'Apr', arpu: 13.90 },
                        { month: 'May', arpu: 16.50 },
                        { month: 'Jun', arpu: 18.20 },
                      ]}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
                      <XAxis
                        dataKey="month"
                        tick={{ fill: colors.text, fontSize: 12 }}
                        tickLine={{ stroke: colors.grid }}
                        axisLine={{ stroke: colors.grid }}
                      />
                      <YAxis
                        tick={{ fill: colors.text, fontSize: 12 }}
                        tickLine={{ stroke: colors.grid }}
                        axisLine={{ stroke: colors.grid }}
                      />
                      <Tooltip formatter={(value) => [`$${value}`, 'ARPU']} />
                      <Bar dataKey="arpu" name="ARPU ($)" fill={colors.quinary} radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}