"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  ChevronsLeft,
  ChevronsRight,
  Users,
  LayoutDashboard,
  GamepadIcon,
  History,
  BarChart2,
  HardDrive,
  Shield,
  LogOut,
  Settings,
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { createClient } from "@/lib/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  title: string;
  isCollapsed: boolean;
}

function NavItem({ href, icon, title, isCollapsed }: NavItemProps) {
  const pathname = usePathname();
  const isActive = pathname === href;

  if (isCollapsed) {
    return (
      <Tooltip delayDuration={0}>
        <TooltipTrigger asChild>
          <Link
            href={href}
            className={cn(
              "flex h-10 w-10 items-center justify-center rounded-md",
              isActive
                ? "bg-accent text-accent-foreground"
                : "hover:bg-accent hover:text-accent-foreground"
            )}
          >
            {icon}
            <span className="sr-only">{title}</span>
          </Link>
        </TooltipTrigger>
        <TooltipContent side="right" className="font-medium">
          {title}
        </TooltipContent>
      </Tooltip>
    );
  }

  return (
    <Link
      href={href}
      className={cn(
        "flex items-center gap-x-2 rounded-md px-3 py-2 text-sm font-medium",
        isActive
          ? "bg-accent text-accent-foreground"
          : "hover:bg-accent hover:text-accent-foreground"
      )}
    >
      {icon}
      {title}
    </Link>
  );
}

export function Sidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { toast } = useToast();
  const supabase = createClient();

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        toast({
          title: "Sign out failed",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Signed out",
        description: "You have been successfully signed out.",
      });

      // Clear any local storage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('supabase.auth.token');
        sessionStorage.clear();
      }

      // Redirect will be handled by auth state change listener
    } catch (error) {
      console.error('Sign out error:', error);
      toast({
        title: "Sign out failed",
        description: "An error occurred while signing out.",
        variant: "destructive",
      });
    }
  };

  return (
    <div
      className={cn(
        "group relative flex h-screen flex-col border-r bg-card transition-all duration-300",
        isCollapsed ? "w-[70px]" : "w-[240px]"
      )}
    >
      <div className="flex h-14 items-center px-3 border-b">
        <Link
          href="/dashboard"
          className={cn(
            "flex items-center gap-2 font-semibold",
            isCollapsed && "justify-center"
          )}
        >
          <GamepadIcon className="h-6 w-6 text-primary" />
          {!isCollapsed && <span>Ludo Admin</span>}
        </Link>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "absolute right-2 top-3",
            isCollapsed && "rotate-180"
          )}
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          {isCollapsed ? <ChevronsRight className="h-4 w-4" /> : <ChevronsLeft className="h-4 w-4" />}
        </Button>
      </div>
      <ScrollArea className="flex-1 py-3">
        <div className={cn("flex flex-col gap-1 px-2", isCollapsed && "items-center")}>
          <NavItem
            href="/dashboard"
            icon={<LayoutDashboard className="h-5 w-5" />}
            title="Overview"
            isCollapsed={isCollapsed}
          />
          <NavItem
            href="/dashboard/users"
            icon={<Users className="h-5 w-5" />}
            title="User Management"
            isCollapsed={isCollapsed}
          />
          <NavItem
            href="/dashboard/game-rooms"
            icon={<GamepadIcon className="h-5 w-5" />}
            title="Game Rooms"
            isCollapsed={isCollapsed}
          />
          <NavItem
            href="/dashboard/history"
            icon={<History className="h-5 w-5" />}
            title="Game History"
            isCollapsed={isCollapsed}
          />
          <NavItem
            href="/dashboard/analytics"
            icon={<BarChart2 className="h-5 w-5" />}
            title="Analytics"
            isCollapsed={isCollapsed}
          />
          <NavItem
            href="/dashboard/storage"
            icon={<HardDrive className="h-5 w-5" />}
            title="Storage"
            isCollapsed={isCollapsed}
          />
          <NavItem
            href="/dashboard/security"
            icon={<Shield className="h-5 w-5" />}
            title="Security & Logs"
            isCollapsed={isCollapsed}
          />
          <NavItem
            href="/dashboard/settings"
            icon={<Settings className="h-5 w-5" />}
            title="Settings"
            isCollapsed={isCollapsed}
          />
        </div>
      </ScrollArea>
      <div className={cn("mt-auto p-2 border-t", isCollapsed && "flex justify-center")}>
        {isCollapsed ? (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" onClick={handleSignOut}>
                <LogOut className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">Sign Out</TooltipContent>
          </Tooltip>
        ) : (
          <Button variant="ghost" className="w-full justify-start" onClick={handleSignOut}>
            <LogOut className="mr-2 h-5 w-5" />
            Sign Out
          </Button>
        )}
      </div>
    </div>
  );
}