import 'package:flutter_test/flutter_test.dart';
import 'package:ludo_app/game/game_engine.dart';
import 'package:ludo_app/models/game_state_model.dart';
import 'package:ludo_app/models/game_participant_model.dart';

void main() {
  group('Game Engine Tests', () {
    late GameStateModel gameState;
    late Player redPlayer;
    late Player bluePlayer;

    setUp(() {
      redPlayer = Player(
        id: 'red_player',
        name: 'Red Player',
        color: PlayerColor.red,
      );
      bluePlayer = Player(
        id: 'blue_player',
        name: 'Blue Player',
        color: PlayerColor.blue,
      );

      gameState = GameStateModel(
        gameId: 'test_game',
        mode: GameMode.local,
        players: [redPlayer, bluePlayer],
        gameStarted: true,
      );
    });

    group('Dice Rolling', () {
      test('should generate values between 1 and 6', () {
        for (int i = 0; i < 100; i++) {
          final diceValue = GameEngine.rollDice();
          expect(diceValue, greaterThanOrEqualTo(1));
          expect(diceValue, lessThanOrEqualTo(6));
        }
      });
    });

    group('Piece Movement', () {
      test('should only allow pieces to move out with a 6', () {
        final piece =
            redPlayer.pieces[0]; // Piece in starting area (position -1)

        // Test with dice values 1-5 (should not be able to move)
        for (int dice = 1; dice <= 5; dice++) {
          expect(GameEngine.canPieceMove(piece, dice, gameState), false);
        }

        // Test with dice value 6 (should be able to move)
        expect(GameEngine.canPieceMove(piece, 6, gameState), true);
      });

      test('should move piece to starting position when rolling 6', () {
        final piece = redPlayer.pieces[0];
        final newPosition = GameEngine.calculateNewPosition(piece, 6);
        expect(
          newPosition,
          equals(GameEngine.startingPositions[PlayerColor.red]),
        );
      });

      test('should handle normal board movement', () {
        final piece = redPlayer.pieces[0].copyWith(position: 5);
        final newPosition = GameEngine.calculateNewPosition(piece, 3);
        expect(newPosition, equals(8));
      });

      test('should handle board wrapping', () {
        // Use a blue piece to avoid home entry logic
        final piece = bluePlayer.pieces[0].copyWith(position: 50);
        final newPosition = GameEngine.calculateNewPosition(piece, 3);
        expect(newPosition, equals(1)); // 50 + 3 - 52 = 1
      });

      test('should handle entering home column', () {
        final piece = redPlayer.pieces[0].copyWith(position: 49);
        final newPosition = GameEngine.calculateNewPosition(piece, 3);
        expect(
          newPosition,
          equals(-2),
        ); // Second position in home column (49+3=52, 52-51-1=0, -(0+1)=-1)
      });

      test('should handle movement within home column', () {
        final piece = redPlayer.pieces[0].copyWith(position: -2);
        final newPosition = GameEngine.calculateNewPosition(piece, 2);
        expect(newPosition, equals(-4));
      });

      test('should prevent overshooting home', () {
        final piece = redPlayer.pieces[0].copyWith(position: -5);
        final newPosition = GameEngine.calculateNewPosition(piece, 3);
        expect(newPosition, equals(-5)); // Can't move, would overshoot
      });

      test('should detect win condition', () {
        final piece = redPlayer.pieces[0].copyWith(position: -5);
        final newPosition = GameEngine.calculateNewPosition(piece, 1);
        expect(newPosition, equals(-6)); // Reached home
      });
    });

    group('Piece Capture', () {
      test('should capture opponent piece on same position', () {
        // Place red piece at position 10
        final redPiece = redPlayer.pieces[0].copyWith(position: 10);
        final updatedRedPlayer = redPlayer.copyWith(
          pieces: [redPiece, ...redPlayer.pieces.skip(1)],
        );

        // Place blue piece at position 8
        final bluePiece = bluePlayer.pieces[0].copyWith(position: 8);
        final updatedBluePlayer = bluePlayer.copyWith(
          pieces: [bluePiece, ...bluePlayer.pieces.skip(1)],
        );

        final testGameState = gameState.copyWith(
          players: [updatedRedPlayer, updatedBluePlayer],
          currentPlayerIndex: 1, // Blue player's turn
        );

        // Move blue piece to position 10 (should capture red piece)
        final newState = GameEngine.executeMove(testGameState, bluePiece.id, 2);

        // Check that red piece was sent back to starting area
        final capturedPiece = newState.players[0].pieces[0];
        expect(capturedPiece.position, equals(-1));
      });

      test('should not capture on safe positions', () {
        // Place pieces on safe position
        final redPiece = redPlayer.pieces[0].copyWith(
          position: 1,
        ); // Safe position
        final updatedRedPlayer = redPlayer.copyWith(
          pieces: [redPiece, ...redPlayer.pieces.skip(1)],
        );

        final bluePiece = bluePlayer.pieces[0].copyWith(position: 0);
        final updatedBluePlayer = bluePlayer.copyWith(
          pieces: [bluePiece, ...bluePlayer.pieces.skip(1)],
        );

        final testGameState = gameState.copyWith(
          players: [updatedRedPlayer, updatedBluePlayer],
          currentPlayerIndex: 1, // Blue player's turn
        );

        // Move blue piece to safe position (should not capture)
        final newState = GameEngine.executeMove(testGameState, bluePiece.id, 1);

        // Check that red piece was not captured
        final redPieceAfter = newState.players[0].pieces[0];
        expect(redPieceAfter.position, equals(1)); // Still at safe position
      });
    });

    group('Turn Management', () {
      test('should give extra turn for rolling 6', () {
        final piece = redPlayer.pieces[0].copyWith(position: 5);
        final updatedPlayer = redPlayer.copyWith(
          pieces: [piece, ...redPlayer.pieces.skip(1)],
        );
        final testGameState = gameState.copyWith(
          players: [updatedPlayer, bluePlayer],
        );

        final newState = GameEngine.executeMove(testGameState, piece.id, 6);
        expect(newState.currentPlayerIndex, equals(0)); // Same player
      });

      test('should give extra turn for capturing piece', () {
        // Setup capture scenario
        final redPiece = redPlayer.pieces[0].copyWith(position: 10);
        final updatedRedPlayer = redPlayer.copyWith(
          pieces: [redPiece, ...redPlayer.pieces.skip(1)],
        );

        final bluePiece = bluePlayer.pieces[0].copyWith(position: 8);
        final updatedBluePlayer = bluePlayer.copyWith(
          pieces: [bluePiece, ...bluePlayer.pieces.skip(1)],
        );

        final testGameState = gameState.copyWith(
          players: [updatedRedPlayer, updatedBluePlayer],
          currentPlayerIndex: 1, // Blue player's turn
        );

        final newState = GameEngine.executeMove(testGameState, bluePiece.id, 2);
        expect(newState.currentPlayerIndex, equals(1)); // Same player (blue)
      });

      test('should pass turn for normal moves', () {
        final piece = redPlayer.pieces[0].copyWith(position: 5);
        final updatedPlayer = redPlayer.copyWith(
          pieces: [piece, ...redPlayer.pieces.skip(1)],
        );
        final testGameState = gameState.copyWith(
          players: [updatedPlayer, bluePlayer],
        );

        final newState = GameEngine.executeMove(testGameState, piece.id, 3);
        expect(newState.currentPlayerIndex, equals(1)); // Next player
      });
    });

    group('Win Condition', () {
      test('should detect win when all pieces reach home', () {
        final finishedPieces =
            redPlayer.pieces
                .map(
                  (piece) => piece.copyWith(position: -6, hasReachedEnd: true),
                )
                .toList();

        final winningPlayer = redPlayer.copyWith(pieces: finishedPieces);

        final hasWon = winningPlayer.pieces.every((p) => p.hasReachedEnd);
        expect(hasWon, true);
      });
    });
  });
}
