{"buildFiles": ["D:\\android\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ludo project\\ludo_app\\android\\app\\.cxx\\Debug\\5n2l542j\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ludo project\\ludo_app\\android\\app\\.cxx\\Debug\\5n2l542j\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\android\\sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\android\\sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}