enum GameRoomStatus { waiting, active, completed, terminated }

enum GameType { classic, quick, tournament }

class GameRoomModel {
  final String id;
  final String name;
  final GameRoomStatus status;
  final int playerLimit;
  final double entryFee;
  final double rewardAmount;
  final DateTime createdAt;
  final DateTime? startedAt;
  final DateTime? endedAt;
  final String? createdBy;
  final GameType gameType;
  final String? roomCode;
  final bool isPrivate;

  GameRoomModel({
    required this.id,
    required this.name,
    required this.status,
    required this.playerLimit,
    required this.entryFee,
    required this.rewardAmount,
    required this.createdAt,
    this.startedAt,
    this.endedAt,
    this.createdBy,
    required this.gameType,
    this.roomCode,
    required this.isPrivate,
  });

  factory GameRoomModel.fromJson(Map<String, dynamic> json) {
    return GameRoomModel(
      id: json['id'] as String,
      name: json['name'] as String,
      status: _parseStatus(json['status'] as String),
      playerLimit: json['player_limit'] as int? ?? 4,
      entryFee: (json['entry_fee'] as num?)?.toDouble() ?? 0.0,
      rewardAmount: (json['reward_amount'] as num?)?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(json['created_at'] as String),
      startedAt: json['started_at'] != null 
          ? DateTime.parse(json['started_at'] as String) 
          : null,
      endedAt: json['ended_at'] != null 
          ? DateTime.parse(json['ended_at'] as String) 
          : null,
      createdBy: json['created_by'] as String?,
      gameType: _parseGameType(json['game_type'] as String? ?? 'classic'),
      roomCode: json['room_code'] as String?,
      isPrivate: json['is_private'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'status': status.name,
      'player_limit': playerLimit,
      'entry_fee': entryFee,
      'reward_amount': rewardAmount,
      'created_at': createdAt.toIso8601String(),
      'started_at': startedAt?.toIso8601String(),
      'ended_at': endedAt?.toIso8601String(),
      'created_by': createdBy,
      'game_type': gameType.name,
      'room_code': roomCode,
      'is_private': isPrivate,
    };
  }

  static GameRoomStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'waiting':
        return GameRoomStatus.waiting;
      case 'active':
        return GameRoomStatus.active;
      case 'completed':
        return GameRoomStatus.completed;
      case 'terminated':
        return GameRoomStatus.terminated;
      default:
        return GameRoomStatus.waiting;
    }
  }

  static GameType _parseGameType(String type) {
    switch (type.toLowerCase()) {
      case 'classic':
        return GameType.classic;
      case 'quick':
        return GameType.quick;
      case 'tournament':
        return GameType.tournament;
      default:
        return GameType.classic;
    }
  }

  GameRoomModel copyWith({
    String? id,
    String? name,
    GameRoomStatus? status,
    int? playerLimit,
    double? entryFee,
    double? rewardAmount,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? endedAt,
    String? createdBy,
    GameType? gameType,
    String? roomCode,
    bool? isPrivate,
  }) {
    return GameRoomModel(
      id: id ?? this.id,
      name: name ?? this.name,
      status: status ?? this.status,
      playerLimit: playerLimit ?? this.playerLimit,
      entryFee: entryFee ?? this.entryFee,
      rewardAmount: rewardAmount ?? this.rewardAmount,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      endedAt: endedAt ?? this.endedAt,
      createdBy: createdBy ?? this.createdBy,
      gameType: gameType ?? this.gameType,
      roomCode: roomCode ?? this.roomCode,
      isPrivate: isPrivate ?? this.isPrivate,
    );
  }
}
