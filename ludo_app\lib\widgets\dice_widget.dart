import 'package:flutter/material.dart';
import '../utils/app_theme.dart';

class DiceWidget extends StatefulWidget {
  final int? value;
  final VoidCallback onRoll;
  final bool canRoll;

  const DiceWidget({
    super.key,
    this.value,
    required this.onRoll,
    this.canRoll = true,
  });

  @override
  State<DiceWidget> createState() => _DiceWidgetState();
}

class _DiceWidgetState extends State<DiceWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isRolling = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _rollDice() async {
    if (!widget.canRoll || _isRolling) return;

    setState(() {
      _isRolling = true;
    });

    // Start rolling animation
    _animationController.repeat();

    // Wait for animation duration
    await Future.delayed(const Duration(milliseconds: 800));

    // Stop animation and show result
    _animationController.stop();
    _animationController.reset();

    setState(() {
      _isRolling = false;
    });

    // Call the roll callback
    widget.onRoll();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Dice
        GestureDetector(
          onTap: _rollDice,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _animationController.value * 2 * 3.14159,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppTheme.primaryRed, width: 3),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Center(
                    child:
                        _isRolling
                            ? const Icon(
                              Icons.casino,
                              size: 40,
                              color: AppTheme.primaryRed,
                            )
                            : _buildDiceFace(widget.value ?? 1),
                  ),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 12),

        // Roll Button
        if (widget.canRoll && !_isRolling)
          ElevatedButton(
            onPressed: _rollDice,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryRed,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text(
              'Roll Dice',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          )
        else if (_isRolling)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            decoration: BoxDecoration(
              color: AppTheme.primaryYellow.withValues(alpha: .2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Rolling...',
              style: TextStyle(
                color: AppTheme.primaryYellow,
                fontWeight: FontWeight.bold,
              ),
            ),
          )
        else
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen.withValues(alpha: .2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Rolled: ${widget.value}',
              style: const TextStyle(
                color: AppTheme.primaryGreen,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDiceFace(int value) {
    // Use dice face image assets named dice_1.png, dice_2.png, etc.
    final assetPath = 'assets/dice/$value.png';

    return Image.asset(
      assetPath,
      width: 48,
      height: 48,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        // Fallback to a placeholder if image not found
        return const Icon(Icons.error_outline, color: Colors.red, size: 48);
      },
    );
  }
}
