#!/usr/bin/env node

/**
 * Database Setup Script for Ludo Admin
 * 
 * This script sets up the database schema and populates it with seed data.
 * Run this script to initialize your Supabase database.
 * 
 * Usage: node scripts/setup-database.js
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials. Please check your .env file.');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTables() {
  console.log('📄 Creating database tables...');

  try {
    // Create users table (extends auth.users)
    console.log('   Creating users table...');
    await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE IF NOT EXISTS public.users (
          id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
          username TEXT UNIQUE NOT NULL,
          email TEXT UNIQUE NOT NULL,
          is_verified BOOLEAN DEFAULT FALSE,
          is_blocked BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          last_login TIMESTAMP WITH TIME ZONE,
          avatar_url TEXT,
          games_played INTEGER DEFAULT 0,
          games_won INTEGER DEFAULT 0,
          total_earnings DECIMAL(10,2) DEFAULT 0.00,
          wallet_balance DECIMAL(10,2) DEFAULT 0.00
        );
      `
    });

    console.log('✅ Tables created successfully');
  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
    // Continue with manual setup instructions
    console.log('\n📝 Please run the following SQL manually in your Supabase SQL editor:');
    console.log('\n-- Copy and paste the contents of supabase/migrations/*.sql files');
    console.log('-- Then copy and paste the contents of supabase/seed.sql');
  }
}

async function setupDatabase() {
  console.log('🚀 Starting database setup...\n');

  try {
    // Test connection
    console.log('🔗 Testing database connection...');
    const { data, error } = await supabase.from('information_schema.tables').select('table_name').limit(1);
    if (error) {
      throw new Error(`Connection failed: ${error.message}`);
    }
    console.log('✅ Database connection successful\n');

    // Run migration files
    const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');

    if (fs.existsSync(migrationsDir)) {
      const migrationFiles = fs.readdirSync(migrationsDir)
        .filter(file => file.endsWith('.sql'))
        .sort();

      console.log('📋 Running migrations...');
      for (const file of migrationFiles) {
        await runSQLFile(path.join(migrationsDir, file));
      }
      console.log('✅ All migrations completed\n');
    }

    // Run seed file
    const seedFile = path.join(__dirname, '..', 'supabase', 'seed.sql');
    if (fs.existsSync(seedFile)) {
      console.log('🌱 Running seed data...');
      await runSQLFile(seedFile);
      console.log('✅ Seed data completed\n');
    }

    // Verify setup
    console.log('🔍 Verifying setup...');
    const { count: usersCount } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });

    const { count: adminCount } = await supabase
      .from('admin_users')
      .select('*', { count: 'exact', head: true });

    const { count: gamesCount } = await supabase
      .from('game_rooms')
      .select('*', { count: 'exact', head: true });

    console.log(`📊 Setup verification:`);
    console.log(`   Users: ${usersCount || 0}`);
    console.log(`   Admin users: ${adminCount || 0}`);
    console.log(`   Game rooms: ${gamesCount || 0}`);

    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Create admin users through Supabase Auth');
    console.log('   2. Add their IDs to the admin_users table');
    console.log('   3. Test the admin login at http://localhost:3000/auth/login');

  } catch (error) {
    console.error('\n❌ Database setup failed:', error.message);
    process.exit(1);
  }
}

// Create a simple SQL execution function for Supabase
async function createExecSQLFunction() {
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          EXECUTE sql_query;
        END;
        $$;
      `
    });

    if (error && !error.message.includes('already exists')) {
      console.log('📝 Creating SQL execution function...');
    }
  } catch (error) {
    // Function might not be needed if we use direct queries
    console.log('ℹ️  Using direct SQL execution method');
  }
}

// Run the setup
if (require.main === module) {
  createExecSQLFunction().then(() => setupDatabase());
}

module.exports = { setupDatabase };
