import 'package:supabase_flutter/supabase_flutter.dart';
// import 'package:uuid/uuid.dart';
import '../config/supabase_config.dart';
import '../models/game_room_model.dart';
import '../models/game_participant_model.dart';
import 'auth_service.dart';

class GameRoomService {
  static final SupabaseClient _client = SupabaseConfig.client;
  // static const _uuid = Uuid();

  // Create a new game room
  static Future<GameRoomModel> createGameRoom({
    required String name,
    required int playerLimit,
    required double entryFee,
    required GameType gameType,
    bool isPrivate = false,
  }) async {
    try {
      final userId = AuthService.currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      final roomCode = isPrivate ? _generateRoomCode() : null;
      final rewardAmount = entryFee * playerLimit * 0.9; // 10% platform fee

      final roomData = {
        'name': name,
        'status': GameRoomStatus.waiting.name,
        'player_limit': playerLimit,
        'entry_fee': entryFee,
        'reward_amount': rewardAmount,
        'created_by': userId,
        'game_type': gameType.name,
        'room_code': roomCode,
        'is_private': isPrivate,
      };

      final response =
          await _client
              .from(TableNames.gameRooms)
              .insert(roomData)
              .select()
              .single();

      return GameRoomModel.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Get available game rooms
  static Future<List<GameRoomModel>> getAvailableRooms({
    GameType? gameType,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      var query = _client
          .from(TableNames.gameRooms)
          .select()
          .eq('status', GameRoomStatus.waiting.name)
          .eq('is_private', false);

      if (gameType != null) {
        query = query.eq('game_type', gameType.name);
      }

      final response = await query
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);
      return response.map((json) => GameRoomModel.fromJson(json)).toList();
    } catch (e) {
      rethrow;
    }
  }

  // Join a game room
  static Future<GameParticipantModel> joinGameRoom(String gameId) async {
    try {
      final userId = AuthService.currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      // Check if room exists and has space
      final room = await getGameRoom(gameId);
      if (room == null) throw Exception('Game room not found');

      if (room.status != GameRoomStatus.waiting) {
        throw Exception('Game room is not accepting players');
      }

      // Get current participants count
      final participants = await getGameParticipants(gameId);
      if (participants.length >= room.playerLimit) {
        throw Exception('Game room is full');
      }

      // Check if user is already in the room
      final existingParticipant =
          participants.where((p) => p.userId == userId).firstOrNull;

      if (existingParticipant != null) {
        throw Exception('You are already in this game room');
      }

      // Assign player color and position
      final usedColors = participants.map((p) => p.playerColor).toSet();
      final availableColors =
          PlayerColor.values
              .where((color) => !usedColors.contains(color))
              .toList();

      if (availableColors.isEmpty) {
        throw Exception('No available player colors');
      }

      final participantData = {
        'game_id': gameId,
        'user_id': userId,
        'position': participants.length + 1,
        'player_color': availableColors.first.name,
        'score': 0,
        'is_winner': false,
      };

      final response =
          await _client
              .from(TableNames.gameParticipants)
              .insert(participantData)
              .select()
              .single();

      // If room is now full, start the game
      if (participants.length + 1 >= room.playerLimit) {
        await _client
            .from(TableNames.gameRooms)
            .update({
              'status': GameRoomStatus.active.name,
              'started_at': DateTime.now().toIso8601String(),
            })
            .eq('id', gameId);
      }

      return GameParticipantModel.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Leave a game room
  static Future<void> leaveGameRoom(String gameId) async {
    try {
      final userId = AuthService.currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      await _client
          .from(TableNames.gameParticipants)
          .update({'left_at': DateTime.now().toIso8601String()})
          .eq('game_id', gameId)
          .eq('user_id', userId);
    } catch (e) {
      rethrow;
    }
  }

  // Get game room by ID
  static Future<GameRoomModel?> getGameRoom(String gameId) async {
    try {
      final response =
          await _client
              .from(TableNames.gameRooms)
              .select()
              .eq('id', gameId)
              .single();

      return GameRoomModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Get game room by room code
  static Future<GameRoomModel?> getGameRoomByCode(String roomCode) async {
    try {
      final response =
          await _client
              .from(TableNames.gameRooms)
              .select()
              .eq('room_code', roomCode)
              .single();

      return GameRoomModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Get game participants
  static Future<List<GameParticipantModel>> getGameParticipants(
    String gameId,
  ) async {
    try {
      final response = await _client
          .from(TableNames.gameParticipants)
          .select()
          .eq('game_id', gameId)
          .isFilter('left_at', null);

      return response
          .map((json) => GameParticipantModel.fromJson(json))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Generate a random room code
  static String _generateRoomCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return List.generate(
      6,
      (index) => chars[DateTime.now().millisecond % chars.length],
    ).join();
  }

  // Listen to game room changes
  static Stream<List<Map<String, dynamic>>> listenToGameRooms() {
    return _client.from(TableNames.gameRooms).stream(primaryKey: ['id']);
  }

  // Listen to game participants changes
  static Stream<List<Map<String, dynamic>>> listenToGameParticipants(
    String gameId,
  ) {
    return _client
        .from(TableNames.gameParticipants)
        .stream(primaryKey: ['id'])
        .eq('game_id', gameId);
  }
}
