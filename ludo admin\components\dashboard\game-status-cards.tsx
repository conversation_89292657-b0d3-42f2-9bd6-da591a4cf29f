"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  GamepadIcon, 
  UsersIcon, 
  TrophyIcon, 
  ArrowUpIcon,
  ArrowDownIcon,
  DollarSignIcon
} from 'lucide-react';

interface StatusCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  description?: string;
  isLoading: boolean;
}

function StatusCard({ title, value, icon, trend, description, isLoading }: StatusCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-10 w-10 rounded-full bg-primary/10 p-2 text-primary flex items-center justify-center">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <>
            <Skeleton className="h-8 w-24 mb-1" />
            {trend && <Skeleton className="h-4 w-32" />}
          </>
        ) : (
          <>
            <div className="text-2xl font-bold">{value}</div>
            {trend && (
              <p className="text-xs text-muted-foreground flex items-center mt-1">
                {trend.isPositive ? (
                  <ArrowUpIcon className="h-3 w-3 text-green-500 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-3 w-3 text-red-500 mr-1" />
                )}
                <span className={trend.isPositive ? "text-green-500" : "text-red-500"}>
                  {trend.value}%
                </span>
                <span className="ml-1 text-muted-foreground">{description}</span>
              </p>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}

interface GameStatusCardsProps {
  isLoading: boolean;
  stats: {
    activeGames: number;
    totalUsers: number;
    completedGames: number;
    revenue: number;
  };
}

export function GameStatusCards({ isLoading, stats }: GameStatusCardsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatusCard
        title="Active Games"
        value={stats.activeGames.toString()}
        icon={<GamepadIcon className="h-5 w-5" />}
        trend={{ value: 12, isPositive: true }}
        description="from last week"
        isLoading={isLoading}
      />
      <StatusCard
        title="Total Users"
        value={stats.totalUsers.toLocaleString()}
        icon={<UsersIcon className="h-5 w-5" />}
        trend={{ value: 8, isPositive: true }}
        description="new users"
        isLoading={isLoading}
      />
      <StatusCard
        title="Games Completed"
        value={stats.completedGames.toLocaleString()}
        icon={<TrophyIcon className="h-5 w-5" />}
        trend={{ value: 3, isPositive: false }}
        description="vs. last month"
        isLoading={isLoading}
      />
      <StatusCard
        title="Entry Fee Revenue"
        value={`$${stats.revenue.toLocaleString()}`}
        icon={<DollarSignIcon className="h-5 w-5" />}
        trend={{ value: 18, isPositive: true }}
        description="vs. last month"
        isLoading={isLoading}
      />
    </div>
  );
}