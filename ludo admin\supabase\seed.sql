-- Insert sample users (these will need to be created through auth first)
-- Note: In a real scenario, these users would be created through Supabase Auth
-- This is just for demonstration purposes

-- Create some sample users in auth.users first (this would normally be done through signup)
-- For now, we'll insert directly into our users table with UUIDs that would come from auth

INSERT INTO public.users (id, username, email, is_verified, games_played, games_won, total_earnings, wallet_balance) VALUES
('550e8400-e29b-41d4-a716-************', 'player1', '<EMAIL>', true, 15, 8, 250.00, 100.00),
('550e8400-e29b-41d4-a716-************', 'player2', '<EMAIL>', true, 12, 5, 150.00, 75.00),
('550e8400-e29b-41d4-a716-************', 'player3', '<EMAIL>', true, 20, 12, 400.00, 200.00),
('550e8400-e29b-41d4-a716-************', 'player4', '<EMAIL>', false, 8, 2, 50.00, 25.00),
('550e8400-e29b-41d4-a716-************', 'player5', '<EMAIL>', true, 25, 15, 600.00, 300.00),
('550e8400-e29b-41d4-a716-************', 'testuser', '<EMAIL>', true, 5, 2, 75.00, 50.00),
('550e8400-e29b-41d4-a716-************', 'gamer123', '<EMAIL>', true, 30, 18, 800.00, 400.00),
('550e8400-e29b-41d4-a716-************', 'ludoking', '<EMAIL>', true, 45, 28, 1200.00, 600.00);

-- Insert admin users
INSERT INTO public.admin_users (id, email, role, permissions) VALUES
('550e8400-e29b-41d4-a716-446655440100', '<EMAIL>', 'super_admin', '["users:read", "users:write", "games:read", "games:write", "admin:read", "admin:write"]'::jsonb),
('550e8400-e29b-41d4-a716-446655440101', '<EMAIL>', 'moderator', '["users:read", "games:read", "games:write"]'::jsonb);

-- Insert sample game rooms
INSERT INTO public.game_rooms (id, name, status, player_limit, entry_fee, reward_amount, created_by, game_type, room_code, is_private) VALUES
('650e8400-e29b-41d4-a716-************', 'Quick Match #1', 'completed', 4, 10.00, 35.00, '550e8400-e29b-41d4-a716-************', 'quick', 'QM001', false),
('650e8400-e29b-41d4-a716-************', 'Tournament Final', 'active', 4, 50.00, 180.00, '550e8400-e29b-41d4-a716-************', 'tournament', 'TF001', false),
('650e8400-e29b-41d4-a716-************', 'Casual Game', 'waiting', 4, 5.00, 18.00, '550e8400-e29b-41d4-a716-************', 'classic', 'CG001', false),
('650e8400-e29b-41d4-a716-************', 'Private Room', 'waiting', 2, 20.00, 36.00, '550e8400-e29b-41d4-a716-************', 'classic', 'PR001', true),
('650e8400-e29b-41d4-a716-************', 'High Stakes', 'completed', 4, 100.00, 360.00, '550e8400-e29b-41d4-a716-************', 'classic', 'HS001', false);

-- Insert game participants
INSERT INTO public.game_participants (game_id, user_id, position, score, is_winner, player_color) VALUES
-- Completed game #1
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1, 4, true, 'red'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 2, 3, false, 'blue'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 3, 2, false, 'green'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 4, 1, false, 'yellow'),

-- Active game #2
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1, 2, false, 'red'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 2, 1, false, 'blue'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 3, 3, false, 'green'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 4, 2, false, 'yellow'),

-- Waiting game #3
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1, 0, false, 'red'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 2, 0, false, 'blue'),

-- Completed game #5
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1, 4, true, 'red'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 2, 3, false, 'blue'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 3, 2, false, 'green'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 4, 1, false, 'yellow');

-- Insert sample transactions
INSERT INTO public.transactions (user_id, type, amount, status, description, reference_id) VALUES
('550e8400-e29b-41d4-a716-************', 'deposit', 100.00, 'completed', 'Wallet deposit', 'DEP001'),
('550e8400-e29b-41d4-a716-************', 'game_entry', -10.00, 'completed', 'Entry fee for Quick Match #1', 'QM001'),
('550e8400-e29b-41d4-a716-************', 'game_reward', 35.00, 'completed', 'Winner reward for Quick Match #1', 'QM001'),
('550e8400-e29b-41d4-a716-************', 'deposit', 75.00, 'completed', 'Wallet deposit', 'DEP002'),
('550e8400-e29b-41d4-a716-************', 'deposit', 200.00, 'completed', 'Wallet deposit', 'DEP003'),
('550e8400-e29b-41d4-a716-************', 'game_entry', -50.00, 'completed', 'Entry fee for Tournament Final', 'TF001'),
('550e8400-e29b-41d4-a716-************', 'deposit', 500.00, 'completed', 'Wallet deposit', 'DEP007'),
('550e8400-e29b-41d4-a716-************', 'game_entry', -100.00, 'completed', 'Entry fee for High Stakes', 'HS001'),
('550e8400-e29b-41d4-a716-************', 'game_reward', 360.00, 'completed', 'Winner reward for High Stakes', 'HS001');

-- Insert sample audit logs
INSERT INTO public.audit_logs (admin_id, action, entity_type, entity_id, details, ip_address) VALUES
('550e8400-e29b-41d4-a716-446655440100', 'CREATE', 'user', '550e8400-e29b-41d4-a716-************', '{"username": "ludoking", "email": "<EMAIL>"}', '*************'),
('550e8400-e29b-41d4-a716-446655440100', 'UPDATE', 'user', '550e8400-e29b-41d4-a716-************', '{"field": "is_verified", "old_value": false, "new_value": true}', '*************'),
('550e8400-e29b-41d4-a716-446655440101', 'TERMINATE', 'game_room', '650e8400-e29b-41d4-a716-************', '{"reason": "completed", "winner": "player1"}', '*************'),
('550e8400-e29b-41d4-a716-446655440100', 'BLOCK', 'user', '550e8400-e29b-41d4-a716-************', '{"reason": "suspicious_activity", "duration": "24h"}', '*************');

-- Update game room timestamps for realism
UPDATE public.game_rooms SET 
    created_at = NOW() - INTERVAL '2 hours',
    started_at = NOW() - INTERVAL '1 hour 30 minutes',
    ended_at = NOW() - INTERVAL '30 minutes'
WHERE status = 'completed';

UPDATE public.game_rooms SET 
    created_at = NOW() - INTERVAL '45 minutes',
    started_at = NOW() - INTERVAL '30 minutes'
WHERE status = 'active';

UPDATE public.game_rooms SET 
    created_at = NOW() - INTERVAL '15 minutes'
WHERE status = 'waiting';
