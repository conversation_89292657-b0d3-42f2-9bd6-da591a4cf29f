"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  MoreHorizontalIcon,
  SearchIcon,
  FilterIcon,
  LockIcon,
  UnlockIcon,
  RefreshCwIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  LoaderCircleIcon
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDistanceToNow, format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Pagination } from '@/components/dashboard/pagination';

interface UserProfile {
  id: string;
  user_id: string;
  username: string;
  avatar_url: string | null;
  games_played: number;
  games_won: number;
  total_points: number;
  created_at: string;
  last_login: string | null;
  is_blocked: boolean;
  verification_status: string;
}

export default function UsersPage() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const [pageSize] = useState(10);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [actionConfirmOpen, setActionConfirmOpen] = useState(false);
  const [actionType, setActionType] = useState<'block' | 'unblock' | 'delete' | 'reset' | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const router = useRouter();
  const { toast } = useToast();
  const supabase = createClient();

  const fetchUsers = useCallback(async (page: number, search: string) => {
    try {
      setIsLoading(true);

      // Calculate pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;

      // Base query
      let query = supabase
        .from('user_profiles')
        .select('*', { count: 'exact' });

      // Add search filter if provided
      if (search) {
        query = query.ilike('username', `%${search}%`);
      }

      // Add pagination
      query = query.range(from, to).order('created_at', { ascending: false });

      const { data, count, error } = await query;

      if (error) throw error;

      setUsers(data || []);
      setTotalUsers(count || 0);
      setTotalPages(Math.ceil((count || 0) / pageSize));
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error loading users",
        description: "Please try refreshing the page",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [pageSize, supabase, toast]);

  useEffect(() => {
    fetchUsers(currentPage, searchQuery);
  }, [currentPage, fetchUsers, pageSize, searchQuery, supabase]);

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page on new search
    fetchUsers(1, searchQuery);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const confirmAction = (user: UserProfile, action: 'block' | 'unblock' | 'delete' | 'reset') => {
    setSelectedUser(user);
    setActionType(action);
    setActionConfirmOpen(true);
  };

  const performAction = async () => {
    if (!selectedUser || !actionType) return;

    setIsProcessing(true);

    try {
      switch (actionType) {
        case 'block':
          await blockUser(selectedUser.user_id);
          break;
        case 'unblock':
          await unblockUser(selectedUser.user_id);
          break;
        case 'delete':
          await deleteUser(selectedUser.user_id);
          break;
        case 'reset':
          await resetPassword(selectedUser.user_id);
          break;
      }
    } catch (error) {
      console.error(`Error performing ${actionType} action:`, error);
      toast({
        title: `Error: ${actionType} failed`,
        description: "Please try again",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
      setActionConfirmOpen(false);
      setSelectedUser(null);
      setActionType(null);
    }
  };

  const blockUser = async (userId: string) => {
    const { error } = await supabase
      .from('users')
      .update({ is_blocked: true })
      .eq('user_id', userId);

    if (error) throw error;

    // Log admin action
    await logAdminAction('block', 'user', userId);

    toast({
      title: "User blocked",
      description: "The user has been blocked successfully",
    });

    // Refresh user list
    fetchUsers(currentPage, searchQuery);
  };

  const unblockUser = async (userId: string) => {
    const { error } = await supabase
      .from('users')
      .update({ is_blocked: false })
      .eq('user_id', userId);

    if (error) throw error;

    // Log admin action
    await logAdminAction('unblock', 'user', userId);

    toast({
      title: "User unblocked",
      description: "The user has been unblocked successfully",
    });

    // Refresh user list
    fetchUsers(currentPage, searchQuery);
  };

  const deleteUser = async (userId: string) => {
    // This would delete the auth user in a real implementation
    // For demo, we'll just update the profile
    const { error } = await supabase
      .from('users')
      .update({ is_blocked: true, verification_status: 'deleted' })
      .eq('user_id', userId);

    if (error) throw error;

    // Log admin action
    await logAdminAction('delete', 'user', userId);

    toast({
      title: "User deleted",
      description: "The user has been marked for deletion",
    });

    // Refresh user list
    fetchUsers(currentPage, searchQuery);
  };

  const resetPassword = async (userId: string) => {
    // In a real implementation, this would send a password reset email
    // For demo, we'll just log the action

    // Log admin action
    await logAdminAction('reset_password', 'user', userId);

    toast({
      title: "Password reset initiated",
      description: "A password reset email has been sent to the user",
    });

    // Refresh user list
    fetchUsers(currentPage, searchQuery);
  };

  const logAdminAction = async (action: string, resourceType: string, resourceId: string) => {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) return;

    await supabase.from('admin_audit_logs').insert({
      admin_id: user.id,
      action,
      resource_type: resourceType,
      resource_id: resourceId,
      details: {},
      ip_address: '127.0.0.1' // In a real implementation, this would be the actual IP
    });
  };

  const getVerificationBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return <Badge variant="outline" className="bg-green-500/10 text-green-700 border-green-500/20">Verified</Badge>;
      case 'pending':
        return <Badge variant="outline" className="bg-amber-500/10 text-amber-700 border-amber-500/20">Pending</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="bg-red-500/10 text-red-700 border-red-500/20">Rejected</Badge>;
      default:
        return <Badge variant="outline" className="bg-muted text-muted-foreground">Unknown</Badge>;
    }
  };

  const getActionDialogContent = () => {
    if (!selectedUser || !actionType) return null;

    const actionTexts = {
      block: {
        title: "Block User",
        description: `Are you sure you want to block ${selectedUser.username}? They will no longer be able to access the platform.`,
        buttonText: "Block User",
        buttonVariant: "destructive" as const
      },
      unblock: {
        title: "Unblock User",
        description: `Are you sure you want to unblock ${selectedUser.username}? They will regain access to the platform.`,
        buttonText: "Unblock User",
        buttonVariant: "default" as const
      },
      delete: {
        title: "Delete User",
        description: `Are you sure you want to delete ${selectedUser.username}? This action cannot be undone.`,
        buttonText: "Delete User",
        buttonVariant: "destructive" as const
      },
      reset: {
        title: "Reset Password",
        description: `Are you sure you want to send a password reset email to ${selectedUser.username}?`,
        buttonText: "Send Reset Email",
        buttonVariant: "default" as const
      }
    };

    const { title, description, buttonText, buttonVariant } = actionTexts[actionType];

    return (
      <>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter className="mt-4">
          <Button
            variant="outline"
            onClick={() => setActionConfirmOpen(false)}
            disabled={isProcessing}
          >
            Cancel
          </Button>
          <Button
            variant={buttonVariant}
            onClick={performAction}
            disabled={isProcessing}
          >
            {isProcessing && <LoaderCircleIcon className="mr-2 h-4 w-4 animate-spin" />}
            {buttonText}
          </Button>
        </DialogFooter>
      </>
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
        <p className="text-muted-foreground">
          View and manage all registered users on the platform
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>
            Total users: {totalUsers}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-grow">
              <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by username..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-9"
              />
            </div>
            <Button
              onClick={handleSearch}
              className="sm:w-auto w-full"
            >
              Search
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="sm:w-auto w-full">
                  <FilterIcon className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>All Users</DropdownMenuItem>
                <DropdownMenuItem>Blocked Users</DropdownMenuItem>
                <DropdownMenuItem>Active Users</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Verified</DropdownMenuItem>
                <DropdownMenuItem>Pending Verification</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Username</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Verification</TableHead>
                  <TableHead>Joined</TableHead>
                  <TableHead className="text-right">Games Played</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-12 ml-auto" /></TableCell>
                      <TableCell><Skeleton className="h-8 w-8 rounded-full ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No users found.
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.username}</TableCell>
                      <TableCell>
                        {user.is_blocked ? (
                          <Badge variant="outline" className="bg-red-500/10 text-red-700 border-red-500/20">
                            Blocked
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-green-500/10 text-green-700 border-green-500/20">
                            Active
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {getVerificationBadge(user.verification_status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-xs">
                            {format(new Date(user.created_at), 'MMM d, yyyy')}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {formatDistanceToNow(new Date(user.created_at), { addSuffix: true })}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">{user.games_played}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontalIcon className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => router.push(`/dashboard/users/${user.id}`)}
                            >
                              View details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {user.is_blocked ? (
                              <DropdownMenuItem
                                onClick={() => confirmAction(user, 'unblock')}
                                className="text-green-600"
                              >
                                <UnlockIcon className="mr-2 h-4 w-4" />
                                Unblock user
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem
                                onClick={() => confirmAction(user, 'block')}
                                className="text-amber-600"
                              >
                                <LockIcon className="mr-2 h-4 w-4" />
                                Block user
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              onClick={() => confirmAction(user, 'reset')}
                            >
                              <RefreshCwIcon className="mr-2 h-4 w-4" />
                              Reset password
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => confirmAction(user, 'delete')}
                              className="text-red-600"
                            >
                              <TrashIcon className="mr-2 h-4 w-4" />
                              Delete account
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </CardFooter>
      </Card>

      <Dialog open={actionConfirmOpen} onOpenChange={setActionConfirmOpen}>
        <DialogContent>
          {getActionDialogContent()}
        </DialogContent>
      </Dialog>
    </div>
  );
}