{"buildFiles": ["D:\\android\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ludo project\\ludo_app\\android\\app\\.cxx\\Debug\\5n2l542j\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ludo project\\ludo_app\\android\\app\\.cxx\\Debug\\5n2l542j\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}