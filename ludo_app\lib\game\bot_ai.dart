import 'dart:math';
import '../models/game_state_model.dart';
import '../models/game_participant_model.dart';
import 'game_engine.dart';

enum BotDifficulty { easy, medium, hard }

class BotAI {
  static final Random _random = Random();

  // Make a move for the bot
  static int? makeBotMove(
    GameStateModel gameState,
    int diceValue,
    BotDifficulty difficulty,
  ) {
    final availableMoves = GameEngine.getAvailableMoves(gameState, diceValue);

    if (availableMoves.isEmpty) {
      return null;
    }

    switch (difficulty) {
      case BotDifficulty.easy:
        return _makeEasyMove(gameState, availableMoves, diceValue);
      case BotDifficulty.medium:
        return _makeMediumMove(gameState, availableMoves, diceValue);
      case BotDifficulty.hard:
        return _makeHardMove(gameState, availableMoves, diceValue);
    }
  }

  // Easy bot - random moves
  static int _makeEasyMove(
    GameStateModel gameState,
    List<int> availableMoves,
    int diceValue,
  ) {
    return availableMoves[_random.nextInt(availableMoves.length)];
  }

  // Medium bot - some strategy
  static int _makeMediumMove(
    GameStateModel gameState,
    List<int> availableMoves,
    int diceValue,
  ) {
    final currentPlayer = gameState.currentPlayer;

    // Priority 1: Move piece out of starting area if possible
    for (final pieceId in availableMoves) {
      final piece = currentPlayer.pieces.firstWhere((p) => p.id == pieceId);
      if (piece.position == -1 && diceValue == 6) {
        return pieceId;
      }
    }

    // Priority 2: Capture opponent pieces
    for (final pieceId in availableMoves) {
      final piece = currentPlayer.pieces.firstWhere((p) => p.id == pieceId);
      final newPosition = GameEngine.calculateNewPosition(piece, diceValue);

      if (_canCaptureOpponent(gameState, newPosition, currentPlayer.color)) {
        return pieceId;
      }
    }

    // Priority 3: Move piece closest to home
    int bestPieceId = availableMoves.first;
    int maxProgress = -1;

    for (final pieceId in availableMoves) {
      final piece = currentPlayer.pieces.firstWhere((p) => p.id == pieceId);
      if (piece.position > maxProgress) {
        maxProgress = piece.position;
        bestPieceId = pieceId;
      }
    }

    return bestPieceId;
  }

  // Hard bot - advanced strategy
  static int _makeHardMove(
    GameStateModel gameState,
    List<int> availableMoves,
    int diceValue,
  ) {
    final currentPlayer = gameState.currentPlayer;
    final moves = <_MoveScore>[];

    // Evaluate each possible move
    for (final pieceId in availableMoves) {
      final piece = currentPlayer.pieces.firstWhere((p) => p.id == pieceId);
      final score = _evaluateMove(gameState, piece, diceValue);
      moves.add(_MoveScore(pieceId, score));
    }

    // Sort by score and pick the best move
    moves.sort((a, b) => b.score.compareTo(a.score));
    return moves.first.pieceId;
  }

  // Evaluate the quality of a move
  static int _evaluateMove(
    GameStateModel gameState,
    GamePiece piece,
    int diceValue,
  ) {
    int score = 0;
    final newPosition = GameEngine.calculateNewPosition(piece, diceValue);

    // Bonus for moving out of starting area
    if (piece.position == -1 && diceValue == 6) {
      score += 100;
    }

    // Bonus for capturing opponent pieces
    if (_canCaptureOpponent(gameState, newPosition, piece.color)) {
      score += 80;
    }

    // Bonus for reaching safe positions
    if (GameEngine.safePositions.contains(newPosition)) {
      score += 30;
    }

    // Bonus for progress towards home
    if (piece.position != -1) {
      score += newPosition - piece.position;
    }

    // Penalty for moving into danger
    if (_isPositionDangerous(gameState, newPosition, piece.color)) {
      score -= 50;
    }

    // Bonus for forming blockades (two pieces on same position)
    final currentPlayer = gameState.players.firstWhere(
      (p) => p.color == piece.color,
    );
    for (final otherPiece in currentPlayer.pieces) {
      if (otherPiece.id != piece.id && otherPiece.position == newPosition) {
        score += 40;
      }
    }

    return score;
  }

  // Check if a position can capture an opponent piece
  static bool _canCaptureOpponent(
    GameStateModel gameState,
    int position,
    PlayerColor currentColor,
  ) {
    for (final player in gameState.players) {
      if (player.color != currentColor) {
        for (final piece in player.pieces) {
          if (piece.position == position &&
              !GameEngine.safePositions.contains(position)) {
            return true;
          }
        }
      }
    }
    return false;
  }

  // Check if a position is dangerous (opponent can capture)
  static bool _isPositionDangerous(
    GameStateModel gameState,
    int position,
    PlayerColor currentColor,
  ) {
    if (GameEngine.safePositions.contains(position)) {
      return false;
    }

    for (final player in gameState.players) {
      if (player.color != currentColor) {
        for (final piece in player.pieces) {
          if (piece.position != -1) {
            // Check if opponent can reach this position with any dice roll
            for (int dice = 1; dice <= 6; dice++) {
              final opponentNewPos = GameEngine.calculateNewPosition(
                piece,
                dice,
              );
              if (opponentNewPos == position) {
                return true;
              }
            }
          }
        }
      }
    }
    return false;
  }

  // Add some randomness to bot moves to make them less predictable
  static int addRandomness(List<int> topMoves, BotDifficulty difficulty) {
    switch (difficulty) {
      case BotDifficulty.easy:
        // 70% random, 30% best move
        return _random.nextDouble() < 0.7
            ? topMoves[_random.nextInt(topMoves.length)]
            : topMoves.first;
      case BotDifficulty.medium:
        // 30% random, 70% best move
        return _random.nextDouble() < 0.3
            ? topMoves[_random.nextInt(min(3, topMoves.length))]
            : topMoves.first;
      case BotDifficulty.hard:
        // 10% random, 90% best move
        return _random.nextDouble() < 0.1
            ? topMoves[_random.nextInt(min(2, topMoves.length))]
            : topMoves.first;
    }
  }
}

class _MoveScore {
  final int pieceId;
  final int score;

  _MoveScore(this.pieceId, this.score);
}
