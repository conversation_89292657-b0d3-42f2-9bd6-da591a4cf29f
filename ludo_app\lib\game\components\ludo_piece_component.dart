import 'package:flame/components.dart';
import 'package:flame/effects.dart';
import 'package:flutter/material.dart';
import '../../models/game_state_model.dart';

class LudoPieceComponent extends PositionComponent {
  GamePiece piece;
  final Color color;
  final VoidCallback? onTap;

  late CircleComponent _pieceBody;
  late CircleComponent _pieceBorder;
  late CircleComponent _pieceHighlight;
  late TextComponent _pieceNumber;

  bool _isHighlighted = false;
  bool _isAnimating = false;

  static const double pieceRadius = 18.0;

  LudoPieceComponent({required this.piece, required this.color, this.onTap})
    : super(
        size: Vector2(pieceRadius * 2, pieceRadius * 2),
        anchor: Anchor.center,
      );

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Create highlight circle (shown when piece can be moved)
    _pieceHighlight = CircleComponent(
      radius: pieceRadius + 4,
      paint:
          Paint()
            ..color = Colors.yellow.withValues(alpha: 0.6)
            ..style = PaintingStyle.stroke
            ..strokeWidth = 3,
      anchor: Anchor.center,
    );
    _pieceHighlight.position = Vector2(pieceRadius, pieceRadius);
    _pieceHighlight.scale = Vector2.zero(); // Initially hidden
    add(_pieceHighlight);

    // Create piece shadow
    final shadow = CircleComponent(
      radius: pieceRadius,
      paint: Paint()..color = Colors.black.withValues(alpha: 0.3),
      anchor: Anchor.center,
    );
    shadow.position = Vector2(pieceRadius + 2, pieceRadius + 2);
    add(shadow);

    // Create piece body
    _pieceBody = CircleComponent(
      radius: pieceRadius,
      paint: Paint()..color = color,
      anchor: Anchor.center,
    );
    _pieceBody.position = Vector2(pieceRadius, pieceRadius);
    add(_pieceBody);

    // Create piece border
    _pieceBorder = CircleComponent(
      radius: pieceRadius,
      paint:
          Paint()
            ..color = Colors.white
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2,
      anchor: Anchor.center,
    );
    _pieceBorder.position = Vector2(pieceRadius, pieceRadius);
    add(_pieceBorder);

    // Create piece number
    _pieceNumber = TextComponent(
      text: '${piece.id + 1}',
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
      anchor: Anchor.center,
    );
    _pieceNumber.position = Vector2(pieceRadius, pieceRadius);
    add(_pieceNumber);

    // Add subtle pulsing animation
    _addIdleAnimation();
  }

  void _addIdleAnimation() {
    final scaleEffect = ScaleEffect.to(
      Vector2.all(1.05),
      EffectController(
        duration: 1.5,
        alternate: true,
        infinite: true,
        curve: Curves.easeInOut,
      ),
    );
    _pieceBody.add(scaleEffect);
  }

  void updatePiece(GamePiece newPiece) {
    piece = newPiece;
    _pieceNumber.text = '${piece.id + 1}';

    // Update visual state based on piece status
    if (piece.hasReachedEnd) {
      _pieceBody.paint.color = color.withValues(alpha: 0.8);
      _pieceBorder.paint.color = const Color(0xFFFFD700);
      _pieceBorder.paint.strokeWidth = 3;
    } else if (piece.isInSafeZone) {
      _pieceBorder.paint.color = Colors.green;
      _pieceBorder.paint.strokeWidth = 2.5;
    } else {
      _pieceBody.paint.color = color;
      _pieceBorder.paint.color = Colors.white;
      _pieceBorder.paint.strokeWidth = 2;
    }
  }

  void moveTo(Vector2 newPosition, {double duration = 0.5}) {
    if (_isAnimating) return;

    _isAnimating = true;

    // Create move animation
    final moveEffect = MoveToEffect(
      newPosition,
      EffectController(duration: duration, curve: Curves.easeInOut),
    );

    // Add bounce effect when reaching destination
    final bounceEffect = ScaleEffect.to(
      Vector2.all(1.2),
      EffectController(duration: 0.1, alternate: true),
    );

    moveEffect.onComplete = () {
      _isAnimating = false;
      _pieceBody.add(bounceEffect);
    };

    add(moveEffect);
  }

  void setHighlighted(bool highlighted) {
    if (_isHighlighted == highlighted) return;

    _isHighlighted = highlighted;

    if (highlighted) {
      // Show highlight with pulsing animation
      _pieceHighlight.add(
        ScaleEffect.to(Vector2.all(1.0), EffectController(duration: 0.2)),
      );

      final pulseEffect = ScaleEffect.to(
        Vector2.all(1.1),
        EffectController(
          duration: 0.8,
          alternate: true,
          infinite: true,
          curve: Curves.easeInOut,
        ),
      );
      _pieceHighlight.add(pulseEffect);

      // Add glow effect to piece body
      _pieceBody.add(
        ColorEffect(
          color.withValues(alpha: 0.8),
          EffectController(duration: 0.8, alternate: true, infinite: true),
        ),
      );
    } else {
      // Hide highlight
      _pieceHighlight.add(
        ScaleEffect.to(Vector2.zero(), EffectController(duration: 0.2)),
      );

      // Remove all effects from highlight
      _pieceHighlight.removeAll(_pieceHighlight.children.whereType<Effect>());

      // Reset piece body color
      _pieceBody.removeAll(_pieceBody.children.whereType<ColorEffect>());
      _pieceBody.paint.color = color;
    }
  }

  void playMoveAnimation() {
    // Play a special animation when piece is moved
    final jumpEffect = MoveByEffect(
      Vector2(0, -10),
      EffectController(duration: 0.2, alternate: true, curve: Curves.easeOut),
    );
    add(jumpEffect);

    // Add rotation effect
    final rotateEffect = RotateEffect.by(
      3.14159 * 2, // Full rotation
      EffectController(duration: 0.4),
    );
    _pieceBody.add(rotateEffect);
  }

  void playCaptureAnimation() {
    // Play animation when this piece captures another
    final scaleEffect = ScaleEffect.to(
      Vector2.all(1.3),
      EffectController(
        duration: 0.3,
        alternate: true,
        curve: Curves.elasticOut,
      ),
    );
    add(scaleEffect);

    // Flash effect
    final flashEffect = ColorEffect(
      Colors.yellow,
      EffectController(duration: 0.1, alternate: true, repeatCount: 3),
    );
    _pieceBody.add(flashEffect);
  }

  void playCapturedAnimation() {
    // Play animation when this piece is captured
    final shakeEffect = MoveByEffect(
      Vector2(5, 0),
      EffectController(duration: 0.1, alternate: true, repeatCount: 3),
    );
    add(shakeEffect);

    // Fade out effect
    final fadeEffect = OpacityEffect.to(
      0.5,
      EffectController(duration: 0.3, alternate: true),
    );
    add(fadeEffect);
  }

  void playFinishAnimation() {
    // Play animation when piece reaches finish
    final celebrationEffect = ScaleEffect.to(
      Vector2.all(1.5),
      EffectController(
        duration: 0.5,
        alternate: true,
        curve: Curves.elasticOut,
      ),
    );
    add(celebrationEffect);

    // Golden glow effect
    final glowEffect = ColorEffect(
      const Color(0xFFFFD700),
      EffectController(duration: 1.0, alternate: true, repeatCount: 2),
    );
    _pieceBody.add(glowEffect);

    // Sparkle effect (simplified)
    for (int i = 0; i < 5; i++) {
      final sparkle = CircleComponent(
        radius: 2,
        paint: Paint()..color = Colors.yellow,
      );
      sparkle.position = Vector2(
        pieceRadius + (i - 2) * 8,
        pieceRadius + (i % 2 == 0 ? -15 : 15),
      );

      final sparkleEffect = OpacityEffect.to(
        0.0,
        EffectController(duration: 0.8),
      );
      sparkle.add(sparkleEffect);

      add(sparkle);

      // Remove sparkle after animation
      Future.delayed(const Duration(milliseconds: 800), () {
        if (sparkle.isMounted) {
          remove(sparkle);
        }
      });
    }
  }

  @override
  bool containsPoint(Vector2 point) {
    // Check if point is within the piece's circular area
    final center = Vector2(pieceRadius, pieceRadius);
    final distance = (point - position - center).length;
    return distance <= pieceRadius + 5; // Add small buffer for easier tapping
  }
}
