"use client";

import { useState, useEffect } from 'react';
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend
} from 'recharts';
import { createClient } from '@/lib/supabase/client';
import { Skeleton } from '@/components/ui/skeleton';
import { useTheme } from 'next-themes';
import { addDays, format, subDays } from 'date-fns';

interface GameDataPoint {
  date: string;
  active: number;
  completed: number;
}

export function GameOverviewChart() {
  const [data, setData] = useState<GameDataPoint[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { theme } = useTheme();
  const supabase = createClient();

  // Get theme-specific colors
  const getColors = () => {
    return {
      active: theme === 'dark' ? 'hsl(var(--chart-1))' : 'hsl(var(--chart-1))',
      completed: theme === 'dark' ? 'hsl(var(--chart-2))' : 'hsl(var(--chart-2))',
      grid: theme === 'dark' ? 'hsl(var(--border))' : 'hsl(var(--border))',
      text: theme === 'dark' ? 'hsl(var(--muted-foreground))' : 'hsl(var(--muted-foreground))'
    };
  };

  const colors = getColors();

  useEffect(() => {
    const fetchGameData = async () => {
      try {
        setIsLoading(true);
        
        // Generate last 30 days dates
        const dateLabels: GameDataPoint[] = [];
        const today = new Date();
        
        for (let i = 29; i >= 0; i--) {
          const date = subDays(today, i);
          dateLabels.push({
            date: format(date, 'MMM dd'),
            active: 0,
            completed: 0
          });
        }
        
        // Fetch active games created in last 30 days
        const thirtyDaysAgo = subDays(today, 30).toISOString();
        
        const { data: activeGamesData, error: activeGamesError } = await supabase
          .from('game_rooms')
          .select('created_at, status')
          .gte('created_at', thirtyDaysAgo)
          .or('status.eq.active,status.eq.waiting');
          
        if (activeGamesError) throw activeGamesError;
        
        // Fetch completed games in last 30 days
        const { data: completedGamesData, error: completedGamesError } = await supabase
          .from('game_rooms')
          .select('created_at')
          .eq('status', 'completed')
          .gte('created_at', thirtyDaysAgo);
          
        if (completedGamesError) throw completedGamesError;
        
        // Process active games
        activeGamesData?.forEach(game => {
          const gameDate = new Date(game.created_at);
          const dateIndex = 29 - Math.floor((today.getTime() - gameDate.getTime()) / (1000 * 60 * 60 * 24));
          
          if (dateIndex >= 0 && dateIndex < 30) {
            dateLabels[dateIndex].active += 1;
          }
        });
        
        // Process completed games
        completedGamesData?.forEach(game => {
          const gameDate = new Date(game.created_at);
          const dateIndex = 29 - Math.floor((today.getTime() - gameDate.getTime()) / (1000 * 60 * 60 * 24));
          
          if (dateIndex >= 0 && dateIndex < 30) {
            dateLabels[dateIndex].completed += 1;
          }
        });
        
        setData(dateLabels);
      } catch (error) {
        console.error('Error fetching game data for chart:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchGameData();
  }, [supabase]);

  if (isLoading) {
    return <Skeleton className="h-[300px] w-full" />;
  }

  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart
        data={data}
        margin={{
          top: 10,
          right: 30,
          left: 0,
          bottom: 0,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
        <XAxis 
          dataKey="date" 
          tickLine={false}
          axisLine={false}
          tick={{ fill: colors.text, fontSize: 12 }}
        />
        <YAxis 
          tickLine={false}
          axisLine={false}
          tick={{ fill: colors.text, fontSize: 12 }}
        />
        <Tooltip 
          contentStyle={{ 
            backgroundColor: theme === 'dark' ? 'hsl(var(--card))' : 'hsl(var(--card))',
            borderColor: 'hsl(var(--border))',
            color: 'hsl(var(--card-foreground))'
          }}
        />
        <Legend />
        <Area 
          type="monotone" 
          dataKey="active" 
          name="Active Games" 
          stroke={colors.active} 
          fill={colors.active} 
          fillOpacity={0.3} 
        />
        <Area 
          type="monotone" 
          dataKey="completed" 
          name="Completed Games" 
          stroke={colors.completed} 
          fill={colors.completed} 
          fillOpacity={0.3} 
        />
      </AreaChart>
    </ResponsiveContainer>
  );
}