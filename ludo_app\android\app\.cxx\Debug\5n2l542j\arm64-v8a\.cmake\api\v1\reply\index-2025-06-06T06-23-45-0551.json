{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/android/sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/android/sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/android/sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/android/sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-198050232f0b7e7d1159.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-f2d1e1ce52223c425562.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6802e192cf1fb0e11edc.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-f2d1e1ce52223c425562.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-6802e192cf1fb0e11edc.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-198050232f0b7e7d1159.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}