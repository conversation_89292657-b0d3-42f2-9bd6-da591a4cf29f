{"buildFiles": ["D:\\android\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ludo project\\ludo_app\\android\\app\\.cxx\\Debug\\5m4u4t6d\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ludo project\\ludo_app\\android\\app\\.cxx\\Debug\\5m4u4t6d\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}