import { createClient } from '@/lib/supabase/client';
import { User, Session } from '@supabase/supabase-js';

export interface AdminUser {
  id: string;
  email: string;
  role: 'admin' | 'super_admin' | 'moderator';
  permissions: string[];
  created_at: string;
  last_login?: string;
}

export interface AuthResult {
  user?: User;
  session?: Session;
  error?: any;
  adminUser?: AdminUser;
}

export interface AuthError {
  message: string;
  code?: string;
  details?: any;
}

/**
 * Sign in with email and password
 */
export async function signIn(email: string, password: string): Promise<AuthResult> {
  const supabase = createClient();

  try {
    // Attempt to sign in
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email.trim().toLowerCase(),
      password,
    });

    if (error) {
      return {
        error: {
          message: getAuthErrorMessage(error.message),
          code: error.message,
          details: error
        }
      };
    }

    if (!data.user || !data.session) {
      return {
        error: {
          message: 'Authentication failed. Please try again.',
          code: 'auth_failed'
        }
      };
    }

    // Check if user is an admin
    const adminResult = await getAdminUser(data.user.id);

    if (adminResult.error) {
      // Sign out the user since they're not an admin
      await supabase.auth.signOut();
      return {
        error: {
          message: 'Admin access required. Please contact your administrator.',
          code: 'admin_required',
          details: adminResult.error
        }
      };
    }

    // Update last login
    if (adminResult.adminUser) {
      await updateLastLogin(adminResult.adminUser.id);
    }

    return {
      user: data.user,
      session: data.session,
      adminUser: adminResult.adminUser
    };

  } catch (error) {
    console.error('Sign in error:', error);
    return {
      error: {
        message: 'An unexpected error occurred. Please try again.',
        code: 'unexpected_error',
        details: error
      }
    };
  }
}

/**
 * Sign out the current user
 */
export async function signOut(): Promise<{ error?: AuthError }> {
  const supabase = createClient();

  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      return {
        error: {
          message: 'Failed to sign out. Please try again.',
          code: error.message,
          details: error
        }
      };
    }

    // Clear any local storage or session data
    if (typeof window !== 'undefined') {
      localStorage.removeItem('supabase.auth.token');
      sessionStorage.clear();
    }

    return {};

  } catch (error) {
    console.error('Sign out error:', error);
    return {
      error: {
        message: 'An error occurred while signing out.',
        code: 'signout_error',
        details: error
      }
    };
  }
}

/**
 * Get the current authenticated user
 */
export async function getCurrentUser(): Promise<{ user?: User; error?: AuthError }> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase.auth.getUser();

    if (error) {
      return {
        error: {
          message: 'Failed to get current user.',
          code: error.message,
          details: error
        }
      };
    }

    return { user: data.user };

  } catch (error) {
    console.error('Get current user error:', error);
    return {
      error: {
        message: 'Failed to retrieve user information.',
        code: 'user_fetch_error',
        details: error
      }
    };
  }
}

/**
 * Get current session
 */
export async function getCurrentSession(): Promise<{ session?: Session; error?: AuthError }> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      return {
        error: {
          message: 'Failed to get current session.',
          code: error.message,
          details: error
        }
      };
    }

    return { session: data.session };

  } catch (error) {
    console.error('Get current session error:', error);
    return {
      error: {
        message: 'Failed to retrieve session information.',
        code: 'session_fetch_error',
        details: error
      }
    };
  }
}

/**
 * Get admin user information
 */
export async function getAdminUser(userId: string): Promise<{ adminUser?: AdminUser; error?: AuthError }> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('admin_users')
      .select('id, email, role, permissions, created_at, last_login')
      .eq('id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return {
          error: {
            message: 'User is not an administrator.',
            code: 'not_admin',
            details: error
          }
        };
      }

      return {
        error: {
          message: 'Failed to verify admin status.',
          code: error.code || 'admin_check_error',
          details: error
        }
      };
    }

    if (!data) {
      return {
        error: {
          message: 'Admin user not found.',
          code: 'admin_not_found'
        }
      };
    }

    return { adminUser: data as AdminUser };

  } catch (error) {
    console.error('Get admin user error:', error);
    return {
      error: {
        message: 'Failed to check admin permissions.',
        code: 'admin_check_error',
        details: error
      }
    };
  }
}

/**
 * Check if user is admin
 */
export async function isAdmin(userId: string): Promise<boolean> {
  const result = await getAdminUser(userId);
  return !result.error && !!result.adminUser;
}

/**
 * Check if user has specific permission
 */
export async function hasPermission(userId: string, permission: string): Promise<boolean> {
  const result = await getAdminUser(userId);

  if (result.error || !result.adminUser) {
    return false;
  }

  // Super admins have all permissions
  if (result.adminUser.role === 'super_admin') {
    return true;
  }

  // Check specific permission
  return result.adminUser.permissions.includes(permission);
}

/**
 * Update last login timestamp
 */
export async function updateLastLogin(userId: string): Promise<void> {
  const supabase = createClient();

  try {
    await supabase
      .from('admin_users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', userId);
  } catch (error) {
    console.error('Update last login error:', error);
    // Don't throw error as this is not critical
  }
}

/**
 * Refresh the current session
 */
export async function refreshSession(): Promise<{ session?: Session; error?: AuthError }> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      return {
        error: {
          message: 'Failed to refresh session.',
          code: error.message,
          details: error
        }
      };
    }

    return { session: data.session };

  } catch (error) {
    console.error('Refresh session error:', error);
    return {
      error: {
        message: 'Failed to refresh authentication.',
        code: 'refresh_error',
        details: error
      }
    };
  }
}

/**
 * Get user-friendly error messages
 */
export function getAuthErrorMessage(errorMessage: string): string {
  const errorMap: Record<string, string> = {
    'Invalid login credentials': 'Invalid email or password. Please check your credentials and try again.',
    'Email not confirmed': 'Please check your email and click the confirmation link before signing in.',
    'Too many requests': 'Too many login attempts. Please wait a few minutes before trying again.',
    'User not found': 'No account found with this email address.',
    'Invalid email': 'Please enter a valid email address.',
    'Password should be at least 6 characters': 'Password must be at least 6 characters long.',
    'signups not allowed': 'New account registration is currently disabled.',
    'Email rate limit exceeded': 'Too many emails sent. Please wait before requesting another.',
  };

  // Check for exact matches first
  if (errorMap[errorMessage]) {
    return errorMap[errorMessage];
  }

  // Check for partial matches
  for (const [key, value] of Object.entries(errorMap)) {
    if (errorMessage.toLowerCase().includes(key.toLowerCase())) {
      return value;
    }
  }

  // Default message for unknown errors
  return 'An authentication error occurred. Please try again or contact support if the problem persists.';
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): { isValid: boolean; message?: string } {
  if (password.length < 6) {
    return { isValid: false, message: 'Password must be at least 6 characters long.' };
  }

  if (password.length > 128) {
    return { isValid: false, message: 'Password must be less than 128 characters long.' };
  }

  return { isValid: true };
}