enum MoveType { normal, capture, home, safe }

class GameMoveModel {
  final String id;
  final String gameId;
  final String userId;
  final int moveNumber;
  final int? diceValue;
  final int? pieceMoved;
  final int? fromPosition;
  final int? toPosition;
  final MoveType? moveType;
  final DateTime createdAt;

  GameMoveModel({
    required this.id,
    required this.gameId,
    required this.userId,
    required this.moveNumber,
    this.diceValue,
    this.pieceMoved,
    this.fromPosition,
    this.toPosition,
    this.moveType,
    required this.createdAt,
  });

  factory GameMoveModel.fromJson(Map<String, dynamic> json) {
    return GameMoveModel(
      id: json['id'] as String,
      gameId: json['game_id'] as String,
      userId: json['user_id'] as String,
      moveNumber: json['move_number'] as int,
      diceValue: json['dice_value'] as int?,
      pieceMoved: json['piece_moved'] as int?,
      fromPosition: json['from_position'] as int?,
      toPosition: json['to_position'] as int?,
      moveType: json['move_type'] != null 
          ? _parseMoveType(json['move_type'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'game_id': gameId,
      'user_id': userId,
      'move_number': moveNumber,
      'dice_value': diceValue,
      'piece_moved': pieceMoved,
      'from_position': fromPosition,
      'to_position': toPosition,
      'move_type': moveType?.name,
      'created_at': createdAt.toIso8601String(),
    };
  }

  static MoveType _parseMoveType(String type) {
    switch (type.toLowerCase()) {
      case 'normal':
        return MoveType.normal;
      case 'capture':
        return MoveType.capture;
      case 'home':
        return MoveType.home;
      case 'safe':
        return MoveType.safe;
      default:
        return MoveType.normal;
    }
  }

  GameMoveModel copyWith({
    String? id,
    String? gameId,
    String? userId,
    int? moveNumber,
    int? diceValue,
    int? pieceMoved,
    int? fromPosition,
    int? toPosition,
    MoveType? moveType,
    DateTime? createdAt,
  }) {
    return GameMoveModel(
      id: id ?? this.id,
      gameId: gameId ?? this.gameId,
      userId: userId ?? this.userId,
      moveNumber: moveNumber ?? this.moveNumber,
      diceValue: diceValue ?? this.diceValue,
      pieceMoved: pieceMoved ?? this.pieceMoved,
      fromPosition: fromPosition ?? this.fromPosition,
      toPosition: toPosition ?? this.toPosition,
      moveType: moveType ?? this.moveType,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
