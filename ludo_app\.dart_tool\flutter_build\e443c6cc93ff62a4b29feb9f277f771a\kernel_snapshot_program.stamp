{"inputs": ["D:\\ludo project\\ludo_app\\.dart_tool\\package_config_subset", "D:\\android\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\common.dart", "D:\\android\\flutter\\bin\\internal\\engine.version", "D:\\android\\flutter\\bin\\internal\\engine.version", "D:\\android\\flutter\\bin\\internal\\engine.version", "D:\\android\\flutter\\bin\\internal\\engine.version", "D:\\ludo project\\ludo_app\\lib\\main.dart", "D:\\ludo project\\ludo_app\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\material.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\flutter_riverpod.dart", "D:\\ludo project\\ludo_app\\lib\\config\\supabase_config.dart", "D:\\ludo project\\ludo_app\\lib\\utils\\app_router.dart", "D:\\ludo project\\ludo_app\\lib\\utils\\app_theme.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\lib\\path_provider_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\shared_preferences_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_android-2.4.1\\lib\\sqflite_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\lib\\url_launcher_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\path_provider_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\shared_preferences_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\lib\\sqflite_darwin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\lib\\url_launcher_ios.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links_linux-1.0.3\\lib\\app_links_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus-5.0.2\\lib\\connectivity_plus.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\lib\\shared_preferences_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\lib\\url_launcher_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\lib\\url_launcher_macos.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\lib\\shared_preferences_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\lib\\url_launcher_windows.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\about.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\action_buttons.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\action_chip.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\app.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\arc.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\autocomplete.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\badge.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\badge_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\banner.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\banner_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\button_style.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\button_style_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\button_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\card.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\card_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\carousel.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\chip.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\chip_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\choice_chip.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\color_scheme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\colors.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\constants.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\curves.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\data_table.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_source.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\date.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\debug.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\dialog.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\divider.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\divider_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\drawer.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_header.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\expand_icon.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\filter_chip.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\icons.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\ink_splash.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\ink_well.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\input_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\input_chip.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\input_decorator.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\magnifier.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\material.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\material_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\material_localizations.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\material_state.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\menu_style.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\menu_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\motion.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\no_splash.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\page.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\radio.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\radio_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\range_slider.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\scaffold.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\search.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\search_anchor.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\selectable_text.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\selection_area.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\shadows.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\slider.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\slider_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\stepper.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\switch.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\switch_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\tab_controller.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\tabs.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\text_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\text_field.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\text_form_field.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\text_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\theme_data.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\time.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\typography.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\widgets.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\riverpod.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\change_notifier_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\consumer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\framework.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase_flutter-2.9.0\\lib\\supabase_flutter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\go_router.dart", "D:\\ludo project\\ludo_app\\lib\\providers\\auth_provider.dart", "D:\\ludo project\\ludo_app\\lib\\screens\\splash_screen.dart", "D:\\ludo project\\ludo_app\\lib\\screens\\auth\\login_screen.dart", "D:\\ludo project\\ludo_app\\lib\\screens\\auth\\register_screen.dart", "D:\\ludo project\\ludo_app\\lib\\screens\\home\\home_screen.dart", "D:\\ludo project\\ludo_app\\lib\\screens\\game\\game_setup_screen.dart", "D:\\ludo project\\ludo_app\\lib\\screens\\game\\game_board_screen.dart", "D:\\ludo project\\ludo_app\\lib\\screens\\game\\game_rooms_screen.dart", "D:\\ludo project\\ludo_app\\lib\\screens\\profile\\profile_screen.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\google_fonts.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\lib\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\shared_preferences_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\shared_preferences_async_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\lib\\sqflite_platform_interface.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\foundation.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\services.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\url_launcher_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_async_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links_platform_interface-2.0.2\\lib\\app_links_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gtk-2.1.0\\lib\\gtk.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus_platform_interface-1.2.4\\lib\\connectivity_plus_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus-5.0.2\\lib\\src\\connectivity_plus_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\local.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\path.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_async_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\folders.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\lib\\src\\messages.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\cupertino.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\scheduler.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\back_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\rendering.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\animation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\gestures.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\collection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\painting.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\characters.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\actions.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\adapter.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\app.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\async.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\autofill.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\banner.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\basic.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\binding.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\container.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\debug.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_boundary.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\feedback.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\flutter_logo.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\form.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\framework.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\heroes.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\image.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\localizations.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\media_query.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\overlay.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\pages.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\router.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\routes.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_floating_header.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\spacer.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\standard_component_type.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\table.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\text.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\texture.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\title.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\transitions.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\viewport.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\visibility.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\state_notifier-1.0.0\\lib\\state_notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\async_notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\common.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\future_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_notifier_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\internals.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\builders.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\change_notifier_provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\change_notifier_provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\internals.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\supabase.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\url_launcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase_flutter-2.9.0\\lib\\src\\flutter_go_true_client_options.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase_flutter-2.9.0\\lib\\src\\local_storage.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase_flutter-2.9.0\\lib\\src\\supabase.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase_flutter-2.9.0\\lib\\src\\supabase_auth.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\configuration.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\delegate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\information_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\match.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\misc\\errors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\misc\\extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\misc\\inherited_router.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\pages\\custom_transition_page.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\route.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\route_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\router.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\state.dart", "D:\\ludo project\\ludo_app\\lib\\models\\user_model.dart", "D:\\ludo project\\ludo_app\\lib\\services\\auth_service.dart", "D:\\ludo project\\ludo_app\\lib\\widgets\\custom_text_field.dart", "D:\\ludo project\\ludo_app\\lib\\widgets\\custom_button.dart", "D:\\ludo project\\ludo_app\\lib\\providers\\game_provider.dart", "D:\\ludo project\\ludo_app\\lib\\widgets\\game_board_widget.dart", "D:\\ludo project\\ludo_app\\lib\\widgets\\player_info_widget.dart", "D:\\ludo project\\ludo_app\\lib\\widgets\\dice_widget.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_a.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_b.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_c.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_d.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_e.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_f.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_h.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_i.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_j.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_k.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_l.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_m.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_n.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_o.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_p.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_q.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_r.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_s.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_t.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_u.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_v.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_w.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_x.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_y.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_z.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\strings.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\messages_async.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\sqflite.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\sqlite_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\lib\\src\\factory_platform.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\annotations.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\assertions.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\binding.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\collections.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\constants.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\debug.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\isolates.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\key.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\licenses.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\node.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\object.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\platform.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\print.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\serialization.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\timeline.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\unicode.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\autofill.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\binding.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\clipboard.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\debug.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\deferred_component.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\flavor.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\font_loader.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\live_text.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\message_codec.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\message_codecs.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\platform_channel.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\platform_views.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\process_text.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\restoration.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\scribe.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\service_extensions.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\spell_check.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\system_channels.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\system_chrome.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\system_navigator.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\system_sound.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\text_boundary.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\text_formatter.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\text_input.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\undo_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\src\\url_launcher_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links_platform_interface-2.0.2\\lib\\app_links_method_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gtk-2.1.0\\lib\\src\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gtk-2.1.0\\lib\\src\\gtk_application.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gtk-2.1.0\\lib\\src\\gtk_application_notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gtk-2.1.0\\lib\\src\\gtk_settings.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus_platform_interface-1.2.4\\lib\\method_channel_connectivity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus_platform_interface-1.2.4\\lib\\src\\enums.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nm-0.5.0\\lib\\nm.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\lib\\xdg_directories.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\context.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\method_channel_shared_preferences.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\ffi.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\guid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\app.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\colors.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\constants.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\debug.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icons.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\picker.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\radio.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\route.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sheet.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\slider.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\switch.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\theme.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\scheduler\\binding.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\scheduler\\debug.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\scheduler\\priority.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\semantics.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\binding.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\box.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\editable.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\error.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\flex.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\flow.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\image.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\layer.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_body.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\object.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\selection.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\stack.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\table.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\table_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\texture.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\tweens.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\view.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\rendering\\wrap.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\animation\\animation.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_style.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\animation\\animations.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\animation\\curves.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\animation\\tween.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\arena.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\binding.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\constants.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\converter.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\debug.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\eager.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\events.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\force_press.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\long_press.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\multitap.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\resampler.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\scale.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\team.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\algorithms.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\boollist.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\canonicalized_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\comparators.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\functions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_zip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\list_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\priority_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\queue_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\unmodifiable_wrappers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\wrappers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\score\\score.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\alignment.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\basic_types.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\binding.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\border_radius.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\borders.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\box_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\box_fit.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\circle_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\clip.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\colors.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\debug.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\geometry.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\gradient.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\image_cache.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\image_provider.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\image_stream.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\inline_span.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\linear_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\oval_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\star_border.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\strut_style.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\text_painter.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\text_span.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\text_style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\extensions.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\constants.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\_web_image_info_io.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\_web_image_io.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\physics.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta_meta.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\builders.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\listenable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\pragma.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\run_guarded.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\async_notifier\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\async_notifier\\auto_dispose_family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\async_notifier\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\async_notifier\\family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_notifier\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_notifier\\auto_dispose_family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_notifier\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_notifier\\family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stack_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\common\\env.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\always_alive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\async_selector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\provider_base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\container.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\listen.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\proxy_provider_listenable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\ref.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\selector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\scheduler.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\value_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\future_provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\future_provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\notifier\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\notifier\\auto_dispose_family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\notifier\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\notifier\\family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_notifier_provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_notifier_provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\functions_client-2.4.2\\lib\\functions_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\gotrue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\postgrest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\realtime_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\storage_client-2.4.0\\lib\\storage_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\auth_user.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\realtime_client_options.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\remove_subscription_result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\supabase_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\supabase_client_options.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\supabase_event_types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\supabase_query_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\supabase_query_schema.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\supabase_realtime_error.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\supabase_stream_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\legacy_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\url_launcher_uri.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\shared_preferences.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase_flutter-2.9.0\\lib\\src\\local_storage_stub.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\async.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\http.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\lib\\logging.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase_flutter-2.9.0\\lib\\src\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase_flutter-2.9.0\\lib\\src\\hot_restart_cleanup_stub.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase_flutter-2.9.0\\lib\\src\\version.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links-6.4.0\\lib\\app_links.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\logging.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\misc\\error_screen.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\pages\\cupertino.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\pages\\material.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\path_utils.dart", "D:\\ludo project\\ludo_app\\lib\\models\\game_state_model.dart", "D:\\ludo project\\ludo_app\\lib\\models\\game_participant_model.dart", "D:\\ludo project\\ludo_app\\lib\\game\\game_engine.dart", "D:\\ludo project\\ludo_app\\lib\\game\\bot_ai.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\game.dart", "D:\\ludo project\\ludo_app\\lib\\game\\flame_ludo_game.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\crypto.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\asset_manifest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\file_io_desktop_and_mobile.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_descriptor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_family_with_variant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_variant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\sqflite_database_factory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\sql.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\database.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\database_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\open_options.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\transaction.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\constant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\sqflite_debug.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\mixin\\platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\lib\\src\\platform_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\lib\\src\\sqflite_import.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\lib\\src\\sqflite_method_channel.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\method_channel_url_launcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gtk-2.1.0\\lib\\src\\gtk_settings_real.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus_platform_interface-1.2.4\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nm-0.5.0\\lib\\src\\network_manager_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_directory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system_entity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_random_access_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\directory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system_entity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\characters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\internal_style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\parsed_path.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\posix.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\url.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\allocation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\arena.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf16.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf8.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\semantics\\binding.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\semantics\\debug.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\empty_unmodifiable_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters_impl.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\physics\\simulation.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\physics\\tolerance.dart", "D:\\android\\flutter\\packages\\flutter\\lib\\src\\physics\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\stack_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\functions_client-2.4.2\\lib\\src\\functions_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\functions_client-2.4.2\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\gotrue_admin_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\gotrue_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\auth_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\auth_response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\auth_state.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\gotrue_async_storage.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\mfa.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\session.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\user.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\user_attributes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\postgrest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\postgrest_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\realtime_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\realtime_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\realtime_presence.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\transformers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\storage_client-2.4.0\\lib\\src\\storage_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\storage_client-2.4.0\\lib\\src\\storage_file_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\storage_client-2.4.0\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\version.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\yet_another_json_isolate-2.1.0\\lib\\yet_another_json_isolate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\auth_http_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\counter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\rxdart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.7.0\\lib\\src\\supabase_stream_filter_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\url_launcher_string.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\type_conversion.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_async.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_legacy.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\async_cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\async_memoizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\byte_collector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\cancelable_operation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\chunked_stream_reader.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\event_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\future.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\stream_consumer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\stream_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\stream_subscription.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\future_group.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\lazy_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\null_stream_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\restartable_timer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\error.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\future.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\value.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\single_subscription_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\sink_base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_closer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_completer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_group.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_completer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_splitter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_subscription_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_zip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\subscription_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\typed_stream_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\streamed_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\byte_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\streamed_response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\lib\\src\\level.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\lib\\src\\log_record.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\lib\\src\\logger.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links-6.4.0\\lib\\src\\app_links.dart", "D:\\ludo project\\ludo_app\\lib\\models\\game_move_model.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\has_collision_detection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\overlay_route.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\route.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\router_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\value_route.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\world_route.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\vector2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\flame_game.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_widget\\game_widget.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\mixins\\has_performance_tracker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\mixins\\single_game_instance.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\notifying_vector2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\transform2d.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\text_paint.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\components.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\events.dart", "D:\\ludo project\\ludo_app\\lib\\game\\components\\ludo_board_component.dart", "D:\\ludo project\\ludo_app\\lib\\game\\components\\ludo_piece_component.dart", "D:\\ludo project\\ludo_app\\lib\\game\\components\\player_home_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\digest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hash.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hmac.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\md5.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha1.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha256.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha512.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\lib\\path_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\factory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\sql_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\batch.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\cursor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\collection_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\path_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\value_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\utils\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\lib\\synchronized.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\arg_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\mixin\\import_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\logger\\sqflite_logger.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\database_file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\platform\\platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\compat.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\factory_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\mixin\\constant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\mixin\\factory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gtk-2.1.0\\lib\\src\\libgtk.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gtk-2.1.0\\lib\\src\\libgtk.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\dbus.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes_dart_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_directory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\breaks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\table.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\chain.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\frame.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\unparsed_frame.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\functions_client-2.4.2\\lib\\src\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\functions_client-2.4.2\\lib\\src\\version.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\api_version.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\version.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\fetch.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\fetch_options.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\gotrue_admin_mfa_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\helper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\jwt_decode-0.3.1\\lib\\jwt_decode.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\retry-3.1.2\\lib\\retry.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\subjects.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\broadcast_stub.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\gotrue_mfa_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.12.0\\lib\\src\\types\\error_code.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\postgrest_filter_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\postgrest_query_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\postgrest_rpc_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\postgrest_transform_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\raw_postgrest_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\response_postgrest_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\version.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\push.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\retry_timer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\message.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\websocket\\websocket.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.3\\lib\\web_socket_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\storage_client-2.4.0\\lib\\src\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\storage_client-2.4.0\\lib\\src\\storage_bucket_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\storage_client-2.4.0\\lib\\src\\version.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\storage_client-2.4.0\\lib\\src\\fetch.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\storage_client-2.4.0\\lib\\src\\file_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\yet_another_json_isolate-2.1.0\\lib\\src\\_isolates_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\rx.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\streams.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\transformers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\url_launcher_string.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_devtools_extension_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\typed\\stream_subscription.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\capture_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\capture_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\release_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\release_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_transformer\\reject_errors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_transformer\\handler_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_transformer\\stream_transformer_wrapper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_transformer\\typed.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\io_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\http_parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_file_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\boundary_characters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\collisions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\decorator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\camera.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\rendering.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component_tree_root.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\dev_tools_service.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\provider_interfaces.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\flame.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_render_box.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_widget\\gesture_detector_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\overlay_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\input.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\geometry.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\text.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\anchor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\camera_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\world.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\screen_hitbox.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\clip_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\components_notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component_key.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\custom_painter_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\fps_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\fps_text_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\advanced_button_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\joystick_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\keyboard_listener_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\toggle_button_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\isometric_tile_map_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\component_viewport_margin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\coordinate_transform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\gesture_hitboxes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_ancestor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_decorator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_game_ref.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_game_reference.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_paint.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_time_scale.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_visibility.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_world.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\ignore_events.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\keyboard_handler.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\parent_is_a.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\single_child_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\snapshot.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\nine_tile_box_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\parallax_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\particle_system_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\position_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\scroll_text_box_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\spawn_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_animation_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_animation_group_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_batch_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_group_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\text_box_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\text_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\text_element_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\timer_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\circle_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\polygon_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\rectangle_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\shape_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\timer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\double_tap_callbacks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\drag_callbacks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\hover_callbacks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\pointer_move_callbacks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\tap_callbacks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\double_tap_dispatcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\multi_drag_dispatcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\multi_tap_dispatcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\pointer_move_dispatcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\game_mixins\\multi_touch_drag_detector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\game_mixins\\multi_touch_tap_detector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\hardware_keyboard_detector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\interfaces\\multi_drag_listener.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\interfaces\\multi_tap_listener.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\double_tap_cancel_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\double_tap_down_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\double_tap_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_cancel_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_end_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_start_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_update_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\pointer_move_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\tap_cancel_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\tap_down_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\tap_up_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\mixins\\keyboard.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\gestures\\detectors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\gestures\\events.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\effects.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\digest_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hash_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha512_fastsinks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\sql_command.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\env_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\lib\\src\\basic_lock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\lib\\src\\reentrant_lock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\lib\\src\\multi_lock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\sqflite_logger.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\platform\\platform_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_address.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_auth_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_introspect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_method_call.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_method_response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_object.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_remote_object.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_remote_object_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_server.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_signal.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_value.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\common.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system_entity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\lazy_chain.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\stack_zone_specification.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\lazy_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\vm_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\behavior_subject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\publish_subject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\replay_subject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\subject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\lib\\src\\version.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.0\\lib\\src\\websocket\\websocket_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.3\\lib\\src\\channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.3\\lib\\src\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\lib\\mime.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\error_and_stacktrace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\combine_latest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\concat.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\concat_eager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\connectable_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\defer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\fork_join.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\from_callable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\merge.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\never.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\race.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\range.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\repeat.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\replay_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\retry.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\retry_when.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\sequence_equal.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\switch_latest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\timer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\using.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\value_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\zip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\debounce.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\pairwise.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\sample.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\throttle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\window.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\default_if_empty.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\delay.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\delay_when.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\dematerialize.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\distinct_unique.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\do.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\end_with.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\end_with_many.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\exhaust_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\flat_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\group_by.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\ignore_elements.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\interval.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\map_not_null.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\map_to.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\materialize.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\max.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\min.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\on_error_resume.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\scan.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\skip_last.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\skip_until.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_many.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\switch_if_empty.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\switch_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_last.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_until.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_while_inclusive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\time_interval.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\timestamp.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\where_not_null.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\where_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\with_latest_from.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\composite_subscription.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\notification.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\io_streamed_response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\authentication_challenge.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\case_insensitive_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\http_date.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\media_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\broadphase.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\prospect_pool.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\has_quadtree_collision_detection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\quad_tree_broadphase.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\quadtree.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\quadtree_collision_detection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\sweep\\sweep.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\collision_callbacks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\collision_detection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\collision_passthrough.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\circle_hitbox.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\composite_hitbox.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\hitbox.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\polygon_hitbox.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\rectangle_hitbox.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\shape_hitbox.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\standard_collision_detection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\raycast_result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\value_cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewport.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component_render_context.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\ordered_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\read_only_ordered_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\paint_decorator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\rotate3d_decorator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\shadow3d_decorator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\transform2d_decorator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\behaviors\\bounded_position_behavior.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\behaviors\\follow_behavior.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewfinder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\circular_viewport.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\fixed_aspect_ratio_viewport.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\fixed_resolution_viewport.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\fixed_size_viewport.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\max_viewport.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\aabb2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\aabb3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\error_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\frustum.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\intersection_result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\noise.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\obb3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\opengl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\plane.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\quad.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\quaternion.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\ray.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\sphere.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\triangle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\utilities.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\recycled_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\component_count_connector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\component_snapshot_connector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\component_tree_connector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\debug_mode_connector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\game_loop_connector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\overlay_navigation_connector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\position_component_attributes_connector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\dev_tools_connector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\assets_cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\images.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\memory_cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\aabb.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\canvas.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\color.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\double.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\fragment_shader.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\image.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\matrix4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\offset.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\paint.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\path.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\picture.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\rect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\rectangle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\size.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\device.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_loop.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\button_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\hud_button_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\hud_margin_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\sprite_button_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\tap_config.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\line.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\line_segment.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\polygon_ray_intersection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\ray2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\shape_intersections.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\glyph.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\line_metrics.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\sprite_font.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\block_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\group_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\group_text_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\inline_text_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\rect_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\rrect_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\sprite_font_text_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\text_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\text_painter_text_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\block_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\bold_text_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\code_text_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\column_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\custom_text_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\document_root.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\group_text_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\header_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\inline_text_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\italic_text_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\paragraph_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\plain_text_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\strikethrough_text_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\text_block_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\text_node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\sprite_font_renderer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\text_renderer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\text_renderer_factory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\background_style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\block_style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\document_style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\flame_text_style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\inline_text_style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\post_process.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\behaviors\\viewport_aware_bounds_behavior.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_by_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_to_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\circle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\rectangle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\rounded_rectangle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\shape.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\experimental.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\layout.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_sheet.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\palette.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\nine_tile_box.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\parallax.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\particles.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\math.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_animation_ticker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_animation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_batch.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\palette.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\solve_quadratic.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_drag_adapter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\tagged_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\position_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\displacement_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\anchor_by_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\anchor_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\anchor_to_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\color_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\component_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\curved_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\delayed_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\duration_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\infinite_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\linear_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\mixins\\has_single_child_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\pause_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\random_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\repeated_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\reverse_curved_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\reverse_linear_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\sequence_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\sine_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\speed_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\zigzag_effect_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\effect_target.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\function_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\glow_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_along_path_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\opacity_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\remove_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\rotate_around_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\rotate_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\scale_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\sequence_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\size_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\transform2d_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\typed_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\database_file_system_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_uuid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\getsid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\getuid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_bus_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_error_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_interface_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_introspectable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_match_rule.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_member_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_message.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_object_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_object_tree.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_peer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_properties.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_read_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_write_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\xml.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_auth_server.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_error.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\empty.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\collection_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.3\\lib\\io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\stream_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.3\\lib\\adapter_web_socket_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\lib\\src\\extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\lib\\src\\mime_multipart_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\lib\\src\\mime_shared.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\lib\\src\\mime_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\subscription.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\backpressure.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\future.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\min_max.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\string_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\scan.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\decoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\encoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\comparing_ordered_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\mapping_ordered_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\callback_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\debug.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\flame.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\matrix_pool.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\random_fallback.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\overflow.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\post_process\\post_process.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\post_process\\post_process_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\measurable_effect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\polygon.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\column_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\layout_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\row_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\layout\\align_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\accelerated_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\composed_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\moving_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\rotating_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\scaled_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\scaling_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\translated_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\circle_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\component_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\computed_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\curved_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\image_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\paint_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\sprite_animation_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\sprite_particle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\solve_cubic.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\image_composition.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\location_context_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\src\\typed_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\typed_buffers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\getsid_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\getuid_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\entities\\default_mapping.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\entities\\entity_mapping.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\entities\\null_mapping.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\enums\\attribute_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\enums\\node_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\format_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\parent_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\parser_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\tag_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\type_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\ancestors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\comparison.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\descendants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\find.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\following.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\mutator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\nodes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\parent.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\preceding.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\sibling.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\string.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_attributes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_children.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_parent.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_visitor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_writer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\attribute.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\cdata.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\comment.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\declaration.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\doctype.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\document.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\document_fragment.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\processing.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\text.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\token.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\visitors\\normalizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\visitors\\pretty_writer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\visitors\\visitor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\visitors\\writer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-1.0.1\\lib\\io_web_socket.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\close_guarantee_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\guarantee_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\stream_channel_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\delegating_stream_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\disconnector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\json_document_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\multi_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\stream_channel_completer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\stream_channel_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-1.0.1\\lib\\web_socket.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\lib\\src\\default_extension_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\lib\\src\\bound_multipart_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\lib\\src\\char_code.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\lib\\src\\magic_number.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\line_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\span_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\string_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\source_span.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\charcodes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\ordered_set_iterator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\queryable_ordered_set_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\debug\\child_counter_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\debug\\time_track_component.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\tmp_vector2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flame-1.29.0\\lib\\image_composition.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\src\\typed_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\dtd\\external_id.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\namespace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\entities\\named_entities.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\core.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\name_matcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\node_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\predicate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\xml_events.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_value.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\prefix_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\simple_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-1.0.1\\lib\\src\\io_web_socket.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-1.0.1\\lib\\src\\web_socket.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\charcode.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\eager_span_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\relative_span_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\location.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\location_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_with_context.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\core\\context.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\core\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\core\\parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\core\\result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\core\\token.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\codec\\event_codec.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\codec\\node_codec.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\converters\\event_decoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\converters\\event_encoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\converters\\node_decoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\converters\\node_encoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\cdata.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\comment.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\declaration.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\doctype.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\end_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\processing.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\start_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\text.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\streams\\each_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\streams\\flatten.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\streams\\normalizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\streams\\subtree_selector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\streams\\with_parent.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\utils\\event_attribute.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\visitor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-1.0.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\term_glyph.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\highlighter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\shared\\annotations.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\matches.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\token.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\newline.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\annotations\\has_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\annotations\\has_location.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\annotations\\has_parent.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\annotations\\annotator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\iterator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\petitparser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\utils\\conversion_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\utils\\list_converter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\utils\\named.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\ascii_glyph_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\glyph_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\unicode_glyph_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\top_level.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\charcode.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\matches\\matches_iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\delegate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\definition.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\expression.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\matcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\character_data_parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\matches\\matches_iterator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\grammar.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\reference.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\resolve.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\expression\\builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\expression\\group.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\accept.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\pattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\cast.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\cast_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\continuation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\flatten.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\permute.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\pick.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\trimming.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\where.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\any_of.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\char.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\digit.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\letter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\lowercase.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\none_of.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\pattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\predicate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\range.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\uppercase.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\whitespace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\word.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\and.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\choice.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\not.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\optional.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\sequence.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\settable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\skip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\eof.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\epsilon.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\failure.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\label.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\position.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\predicate\\any.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\predicate\\character.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\predicate\\pattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\predicate\\predicate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\predicate\\string.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\character.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\greedy.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\lazy.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\limited.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\possessive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\repeating.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\separated.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\separated_by.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\unbounded.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\utils\\failure_joiner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\utils\\labeled.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\utils\\resolvable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\utils\\separated_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\internal\\reference.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\internal\\undefined.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\reflection\\iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\expression\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\expression\\result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\pattern\\parser_pattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\shared\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\utils\\sequential.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\code.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\optimize.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\not.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\constant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_5.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_6.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_7.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_8.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_9.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\pattern\\parser_match.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\pattern\\pattern_iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\lookup.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\pattern\\pattern_iterator.dart"], "outputs": ["D:\\ludo project\\ludo_app\\.dart_tool\\flutter_build\\e443c6cc93ff62a4b29feb9f277f771a\\app.dill", "D:\\ludo project\\ludo_app\\.dart_tool\\flutter_build\\e443c6cc93ff62a4b29feb9f277f771a\\app.dill"]}