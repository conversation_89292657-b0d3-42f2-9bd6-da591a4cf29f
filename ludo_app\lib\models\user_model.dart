class UserModel {
  final String id;
  final String username;
  final String email;
  final bool isVerified;
  final bool isBlocked;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final String? avatarUrl;
  final int gamesPlayed;
  final int gamesWon;
  final double totalEarnings;
  final double walletBalance;

  UserModel({
    required this.id,
    required this.username,
    required this.email,
    required this.isVerified,
    required this.isBlocked,
    required this.createdAt,
    this.lastLogin,
    this.avatarUrl,
    required this.gamesPlayed,
    required this.gamesWon,
    required this.totalEarnings,
    required this.walletBalance,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      isVerified: json['is_verified'] as bool? ?? false,
      isBlocked: json['is_blocked'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastLogin: json['last_login'] != null 
          ? DateTime.parse(json['last_login'] as String) 
          : null,
      avatarUrl: json['avatar_url'] as String?,
      gamesPlayed: json['games_played'] as int? ?? 0,
      gamesWon: json['games_won'] as int? ?? 0,
      totalEarnings: (json['total_earnings'] as num?)?.toDouble() ?? 0.0,
      walletBalance: (json['wallet_balance'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'is_verified': isVerified,
      'is_blocked': isBlocked,
      'created_at': createdAt.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
      'avatar_url': avatarUrl,
      'games_played': gamesPlayed,
      'games_won': gamesWon,
      'total_earnings': totalEarnings,
      'wallet_balance': walletBalance,
    };
  }

  UserModel copyWith({
    String? id,
    String? username,
    String? email,
    bool? isVerified,
    bool? isBlocked,
    DateTime? createdAt,
    DateTime? lastLogin,
    String? avatarUrl,
    int? gamesPlayed,
    int? gamesWon,
    double? totalEarnings,
    double? walletBalance,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      isVerified: isVerified ?? this.isVerified,
      isBlocked: isBlocked ?? this.isBlocked,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      gamesPlayed: gamesPlayed ?? this.gamesPlayed,
      gamesWon: gamesWon ?? this.gamesWon,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      walletBalance: walletBalance ?? this.walletBalance,
    );
  }

  double get winRate => gamesPlayed > 0 ? (gamesWon / gamesPlayed) * 100 : 0.0;
}
