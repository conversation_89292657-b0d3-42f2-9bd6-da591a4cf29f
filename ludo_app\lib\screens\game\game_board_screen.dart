import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/game_provider.dart';
import '../../utils/app_theme.dart';
import '../../widgets/game_board_widget.dart';
import '../../widgets/player_info_widget.dart';
import '../../widgets/dice_widget.dart';

class GameBoardScreen extends ConsumerStatefulWidget {
  final String gameId;
  final bool isSpectator;

  const GameBoardScreen({
    super.key,
    required this.gameId,
    this.isSpectator = false,
  });

  @override
  ConsumerState<GameBoardScreen> createState() => _GameBoardScreenState();
}

class _GameBoardScreenState extends ConsumerState<GameBoardScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize game if it's a local game
    if (widget.gameId == 'local') {
      // Game should already be initialized from setup screen
    }
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider);
    final diceValue = ref.watch(diceValueProvider);

    if (gameState == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('LUDO')),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading game...'),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: AppBar(
        title: Text(widget.isSpectator ? 'Spectating' : 'LUDO'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => _showExitDialog(context),
        ),
        actions: [
          if (!widget.isSpectator)
            IconButton(
              icon: const Icon(Icons.pause),
              onPressed: () => _showPauseDialog(context),
            ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showGameInfo(context, gameState),
          ),
        ],
      ),
      body: SafeArea(
        child: Stack(
          children: [
            // Center Game Board
            Align(
              alignment: Alignment.center,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: GameBoardWidget(
                  gameState: gameState,
                  onPieceMove: widget.isSpectator ? null : _onPieceMove,
                  isSpectator: widget.isSpectator,
                ),
              ),
            ),

            // Top Left: Red Player (or assign accordingly)
            Positioned(
              top: 8,
              left: 8,
              child: _buildPlayerCorner(0, gameState),
            ),

            // Top Right: Green Player
            Positioned(
              top: 8,
              right: 8,
              child: _buildPlayerCorner(1, gameState),
            ),

            // Bottom Left: Blue Player (You - default)
            Positioned(
              bottom: 8,
              left: 8,
              child: _buildPlayerCorner(2, gameState),
            ),

            // Bottom Right: Yellow Player
            Positioned(
              bottom: 8,
              right: 8,
              child: _buildPlayerCorner(3, gameState),
            ),

            // Status Text (center bottom)
            Positioned(
              bottom: 140,
              left: 16,
              right: 16,
              child: _buildGameStatus(gameState, diceValue),
            ),

            // Spectator Controls
            if (widget.isSpectator)
              Positioned(
                bottom: 16,
                left: 16,
                right: 16,
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => context.pop(),
                        icon: const Icon(Icons.exit_to_app),
                        label: const Text('Leave'),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerCorner(int index, dynamic gameState) {
    final player =
        index < gameState.players.length ? gameState.players[index] : null;
    if (player == null) return const SizedBox();

    final isCurrentPlayer = index == gameState.currentPlayerIndex;
    final isBot = player.isBot;
    final isSpectator = widget.isSpectator;

    final isLocalPlayer =
        !isSpectator && !isBot && index == gameState.myPlayerIndex;
    final diceValue = ref.watch(diceValueProvider);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        PlayerInfoWidget(
          player: player,
          isCurrentPlayer: isCurrentPlayer,
          isSpectator: isSpectator,
        ),
        const SizedBox(height: 8),
        if (isLocalPlayer && diceValue == null)
          SizedBox(
            width: 48,
            height: 48,
            child: DiceWidget(
              value: diceValue,
              onRoll: _rollDice,
              canRoll: true,
            ),
          ),
        if (isLocalPlayer && diceValue != null)
          SizedBox(
            width: 48,
            height: 48,
            child: DiceWidget(
              value: diceValue,
              onRoll: _rollDice,
              canRoll: false,
            ),
          ),
      ],
    );
  }

  Widget _buildGameStatus(dynamic gameState, int? diceValue) {
    if (gameState.gameEnded) {
      final winner = gameState.players.firstWhere(
        (p) => p.id == gameState.winnerId,
        orElse: () => null,
      );

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.primaryGreen.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.primaryGreen),
        ),
        child: Column(
          children: [
            Icon(Icons.emoji_events, color: AppTheme.primaryGreen, size: 32),
            const SizedBox(height: 8),
            Text(
              winner != null ? '${winner.name} Wins!' : 'Game Over!',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppTheme.primaryGreen,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => context.pop(),
                    child: const Text('Exit'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _playAgain(),
                    child: const Text('Play Again'),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    String statusText;
    Color statusColor;

    if (widget.isSpectator) {
      statusText = 'Watching: ${gameState.currentPlayer.name}\'s turn';
      statusColor = AppTheme.textSecondary;
    } else if (gameState.currentPlayer.isBot) {
      statusText = '${gameState.currentPlayer.name} is thinking...';
      statusColor = AppTheme.primaryYellow;
    } else {
      if (diceValue == null) {
        statusText = 'Tap dice to roll';
        statusColor = AppTheme.primaryRed;
      } else {
        statusText = 'You rolled $diceValue! Choose a piece to move';
        statusColor = AppTheme.primaryGreen;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: statusColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              statusText,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: statusColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _rollDice() {
    final gameController = ref.read(gameStateProvider.notifier);
    final diceValue = gameController.rollDice();
    ref.read(diceValueProvider.notifier).state = diceValue;
  }

  void _onPieceMove(int pieceId) {
    final diceValue = ref.read(diceValueProvider);
    if (diceValue != null) {
      final gameController = ref.read(gameStateProvider.notifier);
      gameController.makeMove(pieceId, diceValue);
      ref.read(diceValueProvider.notifier).state = null;
    }
  }

  void _showExitDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Exit Game'),
            content: const Text('Are you sure you want to exit the game?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref.read(gameStateProvider.notifier).endGame();
                  context.pop();
                },
                child: const Text('Exit'),
              ),
            ],
          ),
    );
  }

  void _showPauseDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Game Paused'),
            content: const Text('The game has been paused.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Resume'),
              ),
            ],
          ),
    );
  }

  void _showGameInfo(BuildContext context, dynamic gameState) {
    final stats = ref.read(gameStateProvider.notifier).getGameStats();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Game Info'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Game ID: ${gameState.gameId}'),
                Text('Mode: ${gameState.mode.name}'),
                Text('Players: ${gameState.players.length}'),
                Text('Spectators: ${gameState.spectators.length}'),
                Text('Total Moves: ${stats['total_moves']}'),
                Text('Duration: ${stats['game_duration']} minutes'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  void _playAgain() {
    // TODO: Implement play again functionality
    context.pop();
  }
}
