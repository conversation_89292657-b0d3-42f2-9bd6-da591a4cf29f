import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';

class GameRoomsScreen extends ConsumerStatefulWidget {
  const GameRoomsScreen({super.key});

  @override
  ConsumerState<GameRoomsScreen> createState() => _GameRoomsScreenState();
}

class _GameRoomsScreenState extends ConsumerState<GameRoomsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _roomCodeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _roomCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Game Rooms'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.push('/game/setup?mode=online'),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [Tab(text: 'Public Rooms'), Tab(text: 'Join Private')],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildPublicRoomsTab(), _buildJoinPrivateTab()],
      ),
    );
  }

  Widget _buildPublicRoomsTab() {
    return RefreshIndicator(
      onRefresh: () async {
        // TODO: Refresh rooms list
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 5, // Mock data
        itemBuilder: (context, index) {
          return _buildRoomCard(
            roomName: 'Room ${index + 1}',
            playerCount: '${index + 1}/4',
            entryFee: index == 0 ? 'Free' : '\$${(index + 1) * 10}',
            gameType: index % 2 == 0 ? 'Classic' : 'Quick',
            onJoin: () => _joinRoom('room_${index + 1}'),
            onSpectate: () => _spectateRoom('room_${index + 1}'),
          );
        },
      ),
    );
  }

  Widget _buildJoinPrivateTab() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 40),
          Icon(
            Icons.vpn_key,
            size: 80,
            color: AppTheme.primaryRed.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'Join Private Room',
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter the room code shared by your friend',
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppTheme.textSecondary),
          ),
          const SizedBox(height: 40),
          TextFormField(
            controller: _roomCodeController,
            decoration: InputDecoration(
              labelText: 'Room Code',
              hintText: 'Enter 6-digit room code',
              prefixIcon: const Icon(Icons.vpn_key),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              letterSpacing: 4,
              fontWeight: FontWeight.bold,
            ),
            maxLength: 6,
            textCapitalization: TextCapitalization.characters,
          ),
          const SizedBox(height: 24),
          CustomButton(
            text: 'Join Room',
            onPressed: () => _joinPrivateRoom(_roomCodeController.text),
          ),
        ],
      ),
    );
  }

  Widget _buildRoomCard({
    required String roomName,
    required String playerCount,
    required String entryFee,
    required String gameType,
    required VoidCallback onJoin,
    required VoidCallback onSpectate,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        roomName,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        gameType,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    entryFee,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.primaryGreen,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.people, size: 16, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  playerCount,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
                const Spacer(),
                OutlinedButton(
                  onPressed: onSpectate,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    minimumSize: Size.zero,
                  ),
                  child: const Text('Spectate'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: onJoin,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    minimumSize: Size.zero,
                  ),
                  child: const Text('Join'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _joinRoom(String roomId) {
    context.push('/game/board/$roomId');
  }

  void _spectateRoom(String roomId) {
    context.push('/game/board/$roomId?spectator=true');
  }

  void _joinPrivateRoom(String roomCode) {
    if (roomCode.length == 6) {
      // TODO: Implement private room joining
      context.push('/game/board/private_$roomCode');
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid 6-digit room code'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
