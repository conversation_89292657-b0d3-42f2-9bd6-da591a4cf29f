import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flame/game.dart';
import 'package:flutter/material.dart';
import '../models/game_state_model.dart';
import '../models/game_participant_model.dart';
import '../utils/app_theme.dart';
import 'components/ludo_board_component.dart';
import 'components/ludo_piece_component.dart';
import 'components/player_home_component.dart';

class FlameLudoGame extends FlameGame {
  late GameStateModel _gameState;
  late Function(int) _onPieceMove;
  late bool _isSpectator;

  // Game components
  late LudoBoardComponent _boardComponent;
  final Map<String, LudoPieceComponent> _pieceComponents = {};
  final Map<PlayerColor, PlayerHomeComponent> _homeComponents = {};

  // Board configuration
  static const double boardSize = 600.0;
  static const double cellSize = boardSize / 15;
  static const int totalCells = 52;

  // Color mapping
  static const Map<PlayerColor, Color> playerColors = {
    PlayerColor.red: AppTheme.primaryRed,
    PlayerColor.blue: AppTheme.primaryBlue,
    PlayerColor.green: AppTheme.primaryGreen,
    PlayerColor.yellow: AppTheme.primaryYellow,
  };

  void initialize({
    required GameStateModel gameState,
    required Function(int) onPieceMove,
    required bool isSpectator,
  }) {
    _gameState = gameState;
    _onPieceMove = onPieceMove;
    _isSpectator = isSpectator;
  }

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Set camera viewport
    camera.viewfinder.visibleGameSize = Vector2(boardSize, boardSize);

    // Create board component
    _boardComponent = LudoBoardComponent();
    add(_boardComponent);

    // Create home components for each player
    _createHomeComponents();

    // Create piece components
    _createPieceComponents();

    // Update positions based on current game state
    _updateGameState(_gameState);
  }

  void _createHomeComponents() {
    final homePositions = {
      PlayerColor.red: Vector2(cellSize * 1.5, cellSize * 1.5),
      PlayerColor.blue: Vector2(cellSize * 10.5, cellSize * 1.5),
      PlayerColor.green: Vector2(cellSize * 1.5, cellSize * 10.5),
      PlayerColor.yellow: Vector2(cellSize * 10.5, cellSize * 10.5),
    };

    for (final color in PlayerColor.values) {
      final homeComponent = PlayerHomeComponent(
        color: color,
        position: homePositions[color]!,
        size: Vector2(cellSize * 4, cellSize * 4),
      );
      _homeComponents[color] = homeComponent;
      add(homeComponent);
    }
  }

  void _createPieceComponents() {
    for (final player in _gameState.players) {
      for (final piece in player.pieces) {
        final pieceComponent = LudoPieceComponent(
          piece: piece,
          color: playerColors[player.color]!,
          onTap: _isSpectator ? null : () => _onPieceMove(piece.id),
        );

        final pieceKey = '${player.color.name}_${piece.id}';
        _pieceComponents[pieceKey] = pieceComponent;
        add(pieceComponent);
      }
    }
  }

  void updateGameState(GameStateModel newGameState) {
    _gameState = newGameState;
    _updateGameState(newGameState);
  }

  void _updateGameState(GameStateModel gameState) {
    // Update piece positions
    for (final player in gameState.players) {
      for (final piece in player.pieces) {
        final pieceKey = '${player.color.name}_${piece.id}';
        final pieceComponent = _pieceComponents[pieceKey];

        if (pieceComponent != null) {
          final newPosition = _calculatePiecePosition(piece, player.color);
          pieceComponent.moveTo(newPosition);
          pieceComponent.updatePiece(piece);
        }
      }
    }

    // Update home components with piece counts
    for (final player in gameState.players) {
      final homeComponent = _homeComponents[player.color];
      if (homeComponent != null) {
        final piecesInStart =
            player.pieces.where((p) => p.position == -1).length;
        final piecesFinished =
            player.pieces.where((p) => p.hasReachedEnd).length;
        homeComponent.updatePieceCounts(piecesInStart, piecesFinished);
      }
    }
  }

  Vector2 _calculatePiecePosition(GamePiece piece, PlayerColor color) {
    if (piece.position == -1) {
      // Piece is in starting area
      return _getStartingPosition(color, piece.id);
    } else if (piece.hasReachedEnd) {
      // Piece has reached the end
      return _getFinishPosition(color);
    } else {
      // Piece is on the board
      return _getBoardPosition(piece.position);
    }
  }

  Vector2 _getStartingPosition(PlayerColor color, int pieceId) {
    final basePositions = {
      PlayerColor.red: Vector2(cellSize * 2, cellSize * 2),
      PlayerColor.blue: Vector2(cellSize * 11, cellSize * 2),
      PlayerColor.green: Vector2(cellSize * 2, cellSize * 11),
      PlayerColor.yellow: Vector2(cellSize * 11, cellSize * 11),
    };

    final offsets = [
      Vector2(0, 0),
      Vector2(cellSize * 1.5, 0),
      Vector2(0, cellSize * 1.5),
      Vector2(cellSize * 1.5, cellSize * 1.5),
    ];

    return basePositions[color]! + offsets[pieceId];
  }

  Vector2 _getFinishPosition(PlayerColor color) {
    final centerX = boardSize / 2;
    final centerY = boardSize / 2;

    switch (color) {
      case PlayerColor.red:
        return Vector2(centerX, centerY - cellSize);
      case PlayerColor.blue:
        return Vector2(centerX + cellSize, centerY);
      case PlayerColor.green:
        return Vector2(centerX, centerY + cellSize);
      case PlayerColor.yellow:
        return Vector2(centerX - cellSize, centerY);
    }
  }

  Vector2 _getBoardPosition(int position) {
    // Define the path around the board
    final path = _generateBoardPath();

    if (position >= 0 && position < path.length) {
      return path[position];
    }

    // Fallback to center if position is invalid
    return Vector2(boardSize / 2, boardSize / 2);
  }

  List<Vector2> _generateBoardPath() {
    final path = <Vector2>[];

    // Bottom row (left to right) - Red starting area
    for (int i = 1; i <= 5; i++) {
      path.add(Vector2(cellSize * i, cellSize * 8));
    }

    // Right column (bottom to top) - Blue starting area
    for (int i = 7; i >= 1; i--) {
      path.add(Vector2(cellSize * 6, cellSize * i));
    }

    // Top row (left to right)
    for (int i = 7; i <= 13; i++) {
      path.add(Vector2(cellSize * i, cellSize * 6));
    }

    // Right column (top to bottom) - Green starting area
    for (int i = 7; i <= 13; i++) {
      path.add(Vector2(cellSize * 14, cellSize * i));
    }

    // Bottom row (right to left)
    for (int i = 13; i >= 9; i--) {
      path.add(Vector2(cellSize * i, cellSize * 14));
    }

    // Left column (bottom to top) - Yellow starting area
    for (int i = 13; i >= 9; i--) {
      path.add(Vector2(cellSize * 8, cellSize * i));
    }

    // Top row (right to left)
    for (int i = 7; i >= 1; i--) {
      path.add(Vector2(cellSize * i, cellSize * 8));
    }

    // Left column (top to bottom)
    for (int i = 7; i >= 1; i--) {
      path.add(Vector2(cellSize * 0, cellSize * i));
    }

    return path;
  }

  void highlightValidMoves(List<int> validPieceIds) {
    // Reset all piece highlights
    for (final component in _pieceComponents.values) {
      component.setHighlighted(false);
    }

    // Highlight valid pieces
    for (final player in _gameState.players) {
      for (final piece in player.pieces) {
        if (validPieceIds.contains(piece.id)) {
          final pieceKey = '${player.color.name}_${piece.id}';
          final pieceComponent = _pieceComponents[pieceKey];
          pieceComponent?.setHighlighted(true);
        }
      }
    }
  }

  void clearHighlights() {
    for (final component in _pieceComponents.values) {
      component.setHighlighted(false);
    }
  }

  bool onTapDown(TapDownEvent event) {
    if (_isSpectator) return true;

    // Handle tap on board (for piece selection)
    final tapPosition = event.localPosition;

    // Check if tap is on any piece
    for (final entry in _pieceComponents.entries) {
      final component = entry.value;
      if (component.containsPoint(tapPosition)) {
        component.onTap?.call();
        break;
      }
    }

    return true;
  }
}
