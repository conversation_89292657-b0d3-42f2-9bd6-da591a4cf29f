"use client";

import { useState, useEffect } from 'react';
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend
} from 'recharts';
import { createClient } from '@/lib/supabase/client';
import { Skeleton } from '@/components/ui/skeleton';
import { useTheme } from 'next-themes';
import { addDays, format, subDays } from 'date-fns';

interface OverviewDataPoint {
  date: string;
  signups: number;
  activeSessions: number;
}

export function Overview() {
  const [data, setData] = useState<OverviewDataPoint[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { theme } = useTheme();
  const supabase = createClient();

  const colors = {
    signups: theme === 'dark' ? '#8b5cf6' : '#7c3aed',
    activeSessions: theme === 'dark' ? '#06b6d4' : '#0891b2',
    grid: theme === 'dark' ? '#374151' : '#e5e7eb',
    text: theme === 'dark' ? '#d1d5db' : '#374151'
  };

  useEffect(() => {
    const fetchOverviewData = async () => {
      try {
        setIsLoading(true);
        
        // Generate last 30 days dates
        const dateLabels: OverviewDataPoint[] = [];
        const today = new Date();
        
        for (let i = 29; i >= 0; i--) {
          const date = subDays(today, i);
          dateLabels.push({
            date: format(date, 'MMM dd'),
            signups: 0,
            activeSessions: 0
          });
        }
        
        // Fetch user signups in last 30 days
        const thirtyDaysAgo = subDays(today, 30).toISOString();
        
        const { data: usersData, error: usersError } = await supabase
          .from('users')
          .select('created_at')
          .gte('created_at', thirtyDaysAgo);
          
        if (usersError) throw usersError;
        
        // Fetch active game sessions in last 30 days
        const { data: sessionsData, error: sessionsError } = await supabase
          .from('game_participants')
          .select('joined_at')
          .gte('joined_at', thirtyDaysAgo);
          
        if (sessionsError) throw sessionsError;
        
        // Process user signups
        usersData?.forEach(user => {
          const userDate = new Date(user.created_at);
          const dateIndex = 29 - Math.floor((today.getTime() - userDate.getTime()) / (1000 * 60 * 60 * 24));
          
          if (dateIndex >= 0 && dateIndex < 30) {
            dateLabels[dateIndex].signups += 1;
          }
        });
        
        // Process active sessions
        sessionsData?.forEach(session => {
          const sessionDate = new Date(session.joined_at);
          const dateIndex = 29 - Math.floor((today.getTime() - sessionDate.getTime()) / (1000 * 60 * 60 * 24));
          
          if (dateIndex >= 0 && dateIndex < 30) {
            dateLabels[dateIndex].activeSessions += 1;
          }
        });
        
        setData(dateLabels);
      } catch (error) {
        console.error('Error fetching overview data:', error);
        // Set some mock data for demonstration
        const mockData: OverviewDataPoint[] = [];
        const today = new Date();
        
        for (let i = 29; i >= 0; i--) {
          const date = subDays(today, i);
          mockData.push({
            date: format(date, 'MMM dd'),
            signups: Math.floor(Math.random() * 10) + 1,
            activeSessions: Math.floor(Math.random() * 25) + 5
          });
        }
        setData(mockData);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchOverviewData();
  }, [supabase]);

  if (isLoading) {
    return <Skeleton className="h-[300px] w-full" />;
  }

  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart
        data={data}
        margin={{
          top: 10,
          right: 30,
          left: 0,
          bottom: 0,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
        <XAxis 
          dataKey="date" 
          tickLine={false}
          axisLine={false}
          tick={{ fill: colors.text, fontSize: 12 }}
        />
        <YAxis 
          tickLine={false}
          axisLine={false}
          tick={{ fill: colors.text, fontSize: 12 }}
        />
        <Tooltip 
          contentStyle={{ 
            backgroundColor: theme === 'dark' ? 'hsl(var(--card))' : 'hsl(var(--card))',
            borderColor: 'hsl(var(--border))',
            color: 'hsl(var(--card-foreground))'
          }}
        />
        <Legend />
        <Area 
          type="monotone" 
          dataKey="signups" 
          name="New Signups" 
          stroke={colors.signups} 
          fill={colors.signups} 
          fillOpacity={0.3} 
        />
        <Area 
          type="monotone" 
          dataKey="activeSessions" 
          name="Active Sessions" 
          stroke={colors.activeSessions} 
          fill={colors.activeSessions} 
          fillOpacity={0.3} 
        />
      </AreaChart>
    </ResponsiveContainer>
  );
}
