import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/user_model.dart';

class AuthService {
  static final SupabaseClient _client = SupabaseConfig.client;

  // Get current user
  static User? get currentUser => _client.auth.currentUser;

  // Get current user ID
  static String? get currentUserId => currentUser?.id;

  // Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  // Sign up with email and password
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String username,
  }) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: {'display_name': username},
      );

      // if (response.user != null) {
      //   // Create user profile in the users table
      //   await _client.from(TableNames.users).insert({
      //     'id': response.user!.id,
      //     'username': username,
      //     'email': email,
      //     'is_verified': false,
      //     'is_blocked': false,
      //     'games_played': 0,
      //     'games_won': 0,
      //     'total_earnings': 0.0,
      //     'wallet_balance': 0.0,
      //   });
      // }

      return response;
    } catch (e) {
      // TODO: Add proper error logging
      rethrow;
    }
  }

  // Sign in with email and password
  static Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      final user = response.user;
      if (user != null) {
        // Check if profile already exists
        final existing =
            await _client
                .from(TableNames.users)
                .select('id')
                .eq('id', user.id)
                .maybeSingle();

        if (existing == null) {
          // Insert user into users table
          await _client.from(TableNames.users).insert({
            'id': user.id,
            'username': user.userMetadata?['display_name'] ?? 'Unknown',
            'email': user.email,
            'is_verified': true, // User has confirmed email
            'is_blocked': false,
            'games_played': 0,
            'games_won': 0,
            'total_earnings': 0.0,
            'wallet_balance': 0.0,
          });
        }

        // Update last login
        await _client
            .from(TableNames.users)
            .update({'last_login': DateTime.now().toIso8601String()})
            .eq('id', user.id);
      }

      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Sign out
  static Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  // Get user profile
  static Future<UserModel?> getUserProfile([String? userId]) async {
    try {
      final id = userId ?? currentUserId;
      if (id == null) return null;

      final response =
          await _client.from(TableNames.users).select().eq('id', id).single();

      return UserModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Update user profile
  static Future<UserModel?> updateUserProfile({
    String? username,
    String? avatarUrl,
  }) async {
    try {
      if (currentUserId == null) return null;

      final updates = <String, dynamic>{};
      if (username != null) updates['username'] = username;
      if (avatarUrl != null) updates['avatar_url'] = avatarUrl;

      if (updates.isEmpty) return null;

      final response =
          await _client
              .from(TableNames.users)
              .update(updates)
              .eq('id', currentUserId!)
              .select()
              .single();

      return UserModel.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Reset password
  static Future<void> resetPassword(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    }
  }

  // Listen to auth state changes
  static Stream<AuthState> get authStateChanges =>
      _client.auth.onAuthStateChange;
}
