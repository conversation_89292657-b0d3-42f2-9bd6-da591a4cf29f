import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/game_provider.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';

class GameSetupScreen extends ConsumerStatefulWidget {
  final String mode; // 'local' or 'online'

  const GameSetupScreen({super.key, required this.mode});

  @override
  ConsumerState<GameSetupScreen> createState() => _GameSetupScreenState();
}

class _GameSetupScreenState extends ConsumerState<GameSetupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _roomNameController = TextEditingController();

  int _playerCount = 2;
  double _entryFee = 0.0;
  bool _isPrivate = false;
  final List<String> _playerNames = ['Player 1', 'Player 2', '', ''];
  final List<bool> _isBot = [false, false, false, false];

  @override
  void dispose() {
    _roomNameController.dispose();
    super.dispose();
  }

  List<String> _getPlayerColors() {
    // Return color names in order of players
    if (_playerCount == 2) {
      return ['red', 'green'];
    } else if (_playerCount == 3) {
      return ['red', 'blue', 'green'];
    } else {
      return ['red', 'blue', 'green', 'yellow'];
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLocal = widget.mode == 'local';

    return Scaffold(
      appBar: AppBar(
        title: Text(isLocal ? 'Local Game Setup' : 'Create Room'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Room Name (Online only)
                if (!isLocal) ...[
                  CustomTextField(
                    controller: _roomNameController,
                    label: 'Room Name',
                    hintText: 'Enter room name',
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Please enter a room name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                ],

                // Player Count
                _buildPlayerCountSection(),
                const SizedBox(height: 24),

                // Player Configuration
                _buildPlayerConfigSection(),
                const SizedBox(height: 24),

                // Entry Fee (Online only)
                if (!isLocal) ...[
                  _buildEntryFeeSection(),
                  const SizedBox(height: 24),
                ],

                // Privacy Settings (Online only)
                if (!isLocal) ...[
                  _buildPrivacySection(),
                  const SizedBox(height: 32),
                ],

                // Start Game Button
                CustomButton(
                  text: isLocal ? 'Start Local Game' : 'Create Room',
                  onPressed: _startGame,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlayerCountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Number of Players',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        Row(
          children:
              [2, 3, 4].map((count) {
                return Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(right: count == 4 ? 0 : 8),
                    child: _buildPlayerCountCard(count),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildPlayerCountCard(int count) {
    final isSelected = _playerCount == count;

    return GestureDetector(
      onTap: () {
        setState(() {
          _playerCount = count;
          // Reset player names and bot settings for new count
          for (int i = count; i < 4; i++) {
            _playerNames[i] = '';
            _isBot[i] = false;
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryRed : Colors.transparent,
          border: Border.all(
            color: isSelected ? AppTheme.primaryRed : AppTheme.textSecondary,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              Icons.people,
              color: isSelected ? Colors.white : AppTheme.textSecondary,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              '$count Players',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isSelected ? Colors.white : AppTheme.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerConfigSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Player Configuration',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        ...List.generate(_playerCount, (index) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildPlayerConfigCard(index),
          );
        }),
      ],
    );
  }

  Widget _buildPlayerConfigCard(int index) {
    final colors =
        _playerCount == 2
            ? [AppTheme.primaryBlue, AppTheme.primaryYellow]
            : [
              AppTheme.primaryBlue,
              AppTheme.primaryRed,
              AppTheme.primaryGreen,
              AppTheme.primaryYellow,
            ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: colors[index].withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(12),
        color: colors[index].withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: colors[index],
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '${index + 1}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: TextFormField(
              initialValue: _playerNames[index],
              decoration: InputDecoration(
                hintText: 'Player ${index + 1} name',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              onChanged: (value) {
                _playerNames[index] = value;
              },
            ),
          ),
          const SizedBox(width: 12),
          if (widget.mode == 'local')
            Row(
              children: [
                Text('Bot', style: Theme.of(context).textTheme.bodySmall),
                Switch(
                  value: _isBot[index],
                  onChanged: (value) {
                    setState(() {
                      _isBot[index] = value;
                      if (value) {
                        _playerNames[index] = 'Bot ${index + 1}';
                      }
                    });
                  },
                  activeColor: colors[index],
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildEntryFeeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Entry Fee',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        Row(
          children:
              [0.0, 10.0, 25.0, 50.0].map((fee) {
                return Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(right: fee == 50.0 ? 0 : 8),
                    child: _buildEntryFeeCard(fee),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildEntryFeeCard(double fee) {
    final isSelected = _entryFee == fee;

    return GestureDetector(
      onTap: () {
        setState(() {
          _entryFee = fee;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryGreen : Colors.transparent,
          border: Border.all(
            color: isSelected ? AppTheme.primaryGreen : AppTheme.textSecondary,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          fee == 0.0 ? 'Free' : '\$${fee.toInt()}',
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: isSelected ? Colors.white : AppTheme.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildPrivacySection() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Private Room',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              Text(
                'Only players with room code can join',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondary),
              ),
            ],
          ),
        ),
        Switch(
          value: _isPrivate,
          onChanged: (value) {
            setState(() {
              _isPrivate = value;
            });
          },
          activeColor: AppTheme.primaryRed,
        ),
      ],
    );
  }

  void _startGame() {
    if (widget.mode == 'local') {
      // Start local game
      final playerNames = _playerNames.take(_playerCount).toList();
      final botSettings = _isBot.take(_playerCount).toList();
      final playerColors = _getPlayerColors();

      ref
          .read(gameStateProvider.notifier)
          .startLocalGame(
            playerNames: playerNames,
            isBot: botSettings,
            playerColors: playerColors,
          );

      context.push('/game/board/local');
    } else {
      // Create online room
      if (_formKey.currentState?.validate() ?? false) {
        // TODO: Implement room creation
        context.push('/game/rooms');
      }
    }
  }
}
