import 'game_participant_model.dart';
import 'game_move_model.dart';

enum GameMode { local, online }

class GamePiece {
  final int id;
  final PlayerColor color;
  int position;
  bool isInHome;
  bool isInSafeZone;
  bool hasReachedEnd;

  GamePiece({
    required this.id,
    required this.color,
    this.position = -1, // -1 means in starting area
    this.isInHome = false,
    this.isInSafeZone = false,
    this.hasReachedEnd = false,
  });

  GamePiece copyWith({
    int? id,
    PlayerColor? color,
    int? position,
    bool? isInHome,
    bool? isInSafeZone,
    bool? hasReachedEnd,
  }) {
    return GamePiece(
      id: id ?? this.id,
      color: color ?? this.color,
      position: position ?? this.position,
      isInHome: isInHome ?? this.isInHome,
      isInSafeZone: isInSafeZone ?? this.isInSafeZone,
      hasReachedEnd: hasReachedEnd ?? this.hasReachedEnd,
    );
  }
}

class Player {
  final String id;
  final String name;
  final PlayerColor color;
  final bool isBot;
  final bool isSpectator;
  final List<GamePiece> pieces;
  int score;

  Player({
    required this.id,
    required this.name,
    required this.color,
    this.isBot = false,
    this.isSpectator = false,
    List<GamePiece>? pieces,
    this.score = 0,
  }) : pieces = pieces ?? _createPieces(color);

  static List<GamePiece> _createPieces(PlayerColor color) {
    return List.generate(4, (index) => GamePiece(id: index, color: color));
  }

  Player copyWith({
    String? id,
    String? name,
    PlayerColor? color,
    bool? isBot,
    bool? isSpectator,
    List<GamePiece>? pieces,
    int? score,
  }) {
    return Player(
      id: id ?? this.id,
      name: name ?? this.name,
      color: color ?? this.color,
      isBot: isBot ?? this.isBot,
      isSpectator: isSpectator ?? this.isSpectator,
      pieces: pieces ?? this.pieces,
      score: score ?? this.score,
    );
  }
}

class GameStateModel {
  final String gameId;
  final GameMode mode;
  final List<Player> players;
  final List<Player> spectators;
  final int currentPlayerIndex;
  final int? lastDiceValue;
  final bool gameStarted;
  final bool gameEnded;
  final String? winnerId;
  final List<GameMoveModel> moves;
  final DateTime? lastMoveTime;
  final int? myPlayerIndex;

  GameStateModel({
    required this.gameId,
    required this.mode,
    required this.players,
    this.spectators = const [],
    this.currentPlayerIndex = 0,
    this.lastDiceValue,
    this.gameStarted = false,
    this.gameEnded = false,
    this.winnerId,
    this.moves = const [],
    this.lastMoveTime,
    this.myPlayerIndex,
  });

  Player get currentPlayer => players[currentPlayerIndex];

  bool get isLocalGame => mode == GameMode.local;
  bool get isOnlineGame => mode == GameMode.online;

  GameStateModel copyWith({
    String? gameId,
    GameMode? mode,
    List<Player>? players,
    List<Player>? spectators,
    int? currentPlayerIndex,
    int? lastDiceValue,
    bool? gameStarted,
    bool? gameEnded,
    String? winnerId,
    List<GameMoveModel>? moves,
    DateTime? lastMoveTime,
    int? myPlayerIndex,
  }) {
    return GameStateModel(
      gameId: gameId ?? this.gameId,
      mode: mode ?? this.mode,
      players: players ?? this.players,
      spectators: spectators ?? this.spectators,
      currentPlayerIndex: currentPlayerIndex ?? this.currentPlayerIndex,
      lastDiceValue: lastDiceValue ?? this.lastDiceValue,
      gameStarted: gameStarted ?? this.gameStarted,
      gameEnded: gameEnded ?? this.gameEnded,
      winnerId: winnerId ?? this.winnerId,
      moves: moves ?? this.moves,
      lastMoveTime: lastMoveTime ?? this.lastMoveTime,
      myPlayerIndex: myPlayerIndex ?? this.myPlayerIndex,
    );
  }
}
