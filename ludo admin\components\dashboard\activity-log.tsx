"use client";

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { AlertTriangleIcon, CircleCheckIcon, UserIcon, GamepadIcon, DiceIcon } from "lucide-react";

interface Activity {
  id: string;
  user: {
    name: string;
    email: string;
  };
  action: string;
  resourceType: string;
  time: string;
  status: "success" | "warning" | "info";
}

const recentActivity: Activity[] = [
  {
    id: "1",
    user: {
      name: "Admin User",
      email: "<EMAIL>",
    },
    action: "blocked",
    resourceType: "user",
    time: "2 minutes ago",
    status: "warning",
  },
  {
    id: "2",
    user: {
      name: "Admin User",
      email: "<EMAIL>",
    },
    action: "created",
    resourceType: "game room",
    time: "5 minutes ago",
    status: "success",
  },
  {
    id: "3",
    user: {
      name: "Admin User",
      email: "<EMAIL>",
    },
    action: "terminated",
    resourceType: "game",
    time: "1 hour ago",
    status: "warning",
  },
  {
    id: "4",
    user: {
      name: "Admin User",
      email: "<EMAIL>",
    },
    action: "reset password for",
    resourceType: "user",
    time: "3 hours ago",
    status: "info",
  },
  {
    id: "5",
    user: {
      name: "Admin User",
      email: "<EMAIL>",
    },
    action: "updated",
    resourceType: "game configuration",
    time: "5 hours ago",
    status: "success",
  },
];

export function ActivityLog() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>
          Recent administrative actions in the system
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all">
          <TabsList className="mb-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="games">Games</TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="space-y-4">
            {recentActivity.map((activity) => (
              <div
                key={activity.id}
                className="flex items-center gap-4 rounded-lg border p-3"
              >
                <Avatar>
                  <AvatarFallback>
                    {activity.user.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {activity.user.name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {activity.action}{" "}
                    <span className="font-medium">{activity.resourceType}</span>
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {activity.status === "success" && (
                    <CircleCheckIcon className="h-5 w-5 text-green-500" />
                  )}
                  {activity.status === "warning" && (
                    <AlertTriangleIcon className="h-5 w-5 text-amber-500" />
                  )}
                  {activity.status === "info" && (
                    <UserIcon className="h-5 w-5 text-blue-500" />
                  )}
                  <span className="text-xs text-muted-foreground">
                    {activity.time}
                  </span>
                </div>
              </div>
            ))}
          </TabsContent>
          <TabsContent value="users" className="space-y-4">
            {recentActivity
              .filter((a) => a.resourceType === "user")
              .map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-center gap-4 rounded-lg border p-3"
                >
                  <Avatar>
                    <AvatarFallback>
                      {activity.user.name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {activity.user.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {activity.action}{" "}
                      <span className="font-medium">{activity.resourceType}</span>
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {activity.status === "warning" && (
                      <AlertTriangleIcon className="h-5 w-5 text-amber-500" />
                    )}
                    {activity.status === "info" && (
                      <UserIcon className="h-5 w-5 text-blue-500" />
                    )}
                    <span className="text-xs text-muted-foreground">
                      {activity.time}
                    </span>
                  </div>
                </div>
              ))}
          </TabsContent>
          <TabsContent value="games" className="space-y-4">
            {recentActivity
              .filter((a) => a.resourceType.includes("game"))
              .map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-center gap-4 rounded-lg border p-3"
                >
                  <Avatar>
                    <AvatarFallback>
                      {activity.user.name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {activity.user.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {activity.action}{" "}
                      <span className="font-medium">{activity.resourceType}</span>
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {activity.status === "success" && (
                      <CircleCheckIcon className="h-5 w-5 text-green-500" />
                    )}
                    {activity.status === "warning" && (
                      <AlertTriangleIcon className="h-5 w-5 text-amber-500" />
                    )}
                    <span className="text-xs text-muted-foreground">
                      {activity.time}
                    </span>
                  </div>
                </div>
              ))}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}