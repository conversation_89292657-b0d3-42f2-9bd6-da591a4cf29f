{"buildFiles": ["D:\\android\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ludo project\\ludo_app\\android\\app\\.cxx\\Debug\\5m4u4t6d\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ludo project\\ludo_app\\android\\app\\.cxx\\Debug\\5m4u4t6d\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\android\\sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\android\\sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}