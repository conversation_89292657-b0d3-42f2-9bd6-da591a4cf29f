-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    is_blocked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    avatar_url TEXT,
    games_played INTEGER DEFAULT 0,
    games_won INTEGER DEFAULT 0,
    total_earnings DECIMAL(10,2) DEFAULT 0.00,
    wallet_balance DECIMAL(10,2) DEFAULT 0.00
);

-- Create admin_users table
CREATE TABLE public.admin_users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    role TEXT DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin', 'moderator')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    permissions JSONB DEFAULT '[]'::jsonb
);

-- Create game_rooms table
CREATE TABLE public.game_rooms (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    status TEXT DEFAULT 'waiting' CHECK (status IN ('waiting', 'active', 'completed', 'terminated')),
    player_limit INTEGER DEFAULT 4 CHECK (player_limit BETWEEN 2 AND 4),
    entry_fee DECIMAL(10,2) DEFAULT 0.00,
    reward_amount DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES public.users(id),
    game_type TEXT DEFAULT 'classic' CHECK (game_type IN ('classic', 'quick', 'tournament')),
    room_code TEXT UNIQUE,
    is_private BOOLEAN DEFAULT FALSE
);

-- Create game_participants table
CREATE TABLE public.game_participants (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    game_id UUID REFERENCES public.game_rooms(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    position INTEGER CHECK (position BETWEEN 1 AND 4),
    score INTEGER DEFAULT 0,
    is_winner BOOLEAN DEFAULT FALSE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    player_color TEXT CHECK (player_color IN ('red', 'blue', 'green', 'yellow')),
    UNIQUE(game_id, user_id),
    UNIQUE(game_id, position),
    UNIQUE(game_id, player_color)
);

-- Create audit_logs table
CREATE TABLE public.audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    admin_id UUID REFERENCES public.admin_users(id),
    action TEXT NOT NULL,
    entity_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    details JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);

-- Create user_profiles table (for compatibility with existing components)
CREATE VIEW public.user_profiles AS
SELECT 
    id,
    username,
    email,
    avatar_url,
    created_at,
    games_played,
    games_won,
    is_verified,
    is_blocked,
    total_earnings,
    wallet_balance
FROM public.users;

-- Create transactions table for financial tracking
CREATE TABLE public.transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'game_entry', 'game_reward', 'refund')),
    amount DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    description TEXT,
    reference_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Create game_moves table for game state tracking
CREATE TABLE public.game_moves (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    game_id UUID REFERENCES public.game_rooms(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    move_number INTEGER NOT NULL,
    dice_value INTEGER CHECK (dice_value BETWEEN 1 AND 6),
    piece_moved INTEGER,
    from_position INTEGER,
    to_position INTEGER,
    move_type TEXT CHECK (move_type IN ('normal', 'capture', 'home', 'safe')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
