import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/game_state_model.dart';
import '../models/game_participant_model.dart';
import '../game/game_engine.dart';
import '../game/bot_ai.dart';
// import '../services/auth_service.dart';

// Game state provider
final gameStateProvider =
    StateNotifierProvider<GameController, GameStateModel?>((ref) {
      return GameController();
    });

// Current dice value provider
final diceValueProvider = StateProvider<int?>((ref) => null);

// Available moves provider
final availableMovesProvider = Provider<List<int>>((ref) {
  final gameState = ref.watch(gameStateProvider);
  final diceValue = ref.watch(diceValueProvider);

  if (gameState == null || diceValue == null) return [];

  return GameEngine.getAvailableMoves(gameState, diceValue);
});

class GameController extends StateNotifier<GameStateModel?> {
  GameController() : super(null);

  // Start a local game
  void startLocalGame({
    required List<String> playerNames,
    required List<bool> isBot,
    required List<String> playerColors,
    BotDifficulty botDifficulty = BotDifficulty.medium,
  }) {
    final players = <Player>[];
    final colors = PlayerColor.values;

    for (int i = 0; i < playerNames.length; i++) {
      players.add(
        Player(
          id: 'player_$i',
          name: playerNames[i],
          color: colors[i],
          isBot: isBot[i],
        ),
      );
    }

    state = GameStateModel(
      gameId: 'local_${DateTime.now().millisecondsSinceEpoch}',
      mode: GameMode.local,
      players: players,
      gameStarted: true,
    );
  }

  // Start an online game
  void startOnlineGame({
    required String gameId,
    required List<Player> players,
    required List<Player> spectators,
  }) {
    state = GameStateModel(
      gameId: gameId,
      mode: GameMode.online,
      players: players,
      spectators: spectators,
      gameStarted: true,
    );
  }

  // Roll dice
  int rollDice() {
    if (state == null) return 0;

    final diceValue = GameEngine.rollDice();

    // If current player is a bot, make the move automatically
    if (state!.currentPlayer.isBot) {
      _makeBotMove(diceValue);
    }

    return diceValue;
  }

  // Make a move
  void makeMove(int pieceId, int diceValue) {
    if (state == null) return;

    try {
      final newState = GameEngine.executeMove(state!, pieceId, diceValue);
      state = newState;

      // If it's still the same player's turn and they're a bot, make another move
      if (newState.currentPlayer.isBot && !newState.gameEnded) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          final nextDiceValue = rollDice();
          _makeBotMove(nextDiceValue);
        });
      }
    } catch (e) {
      // Handle invalid move
      // TODO: Show error message to user
    }
  }

  // Make a bot move
  void _makeBotMove(int diceValue) {
    if (state == null || !state!.currentPlayer.isBot) return;

    final pieceId = BotAI.makeBotMove(state!, diceValue, BotDifficulty.medium);

    if (pieceId != null) {
      Future.delayed(const Duration(milliseconds: 1500), () {
        makeMove(pieceId, diceValue);
      });
    } else {
      // No valid moves, pass turn
      _passTurn();
    }
  }

  // Pass turn to next player
  void _passTurn() {
    if (state == null) return;

    final nextPlayerIndex =
        (state!.currentPlayerIndex + 1) % state!.players.length;
    state = state!.copyWith(currentPlayerIndex: nextPlayerIndex);

    // If next player is also a bot, roll dice for them
    if (state!.currentPlayer.isBot && !state!.gameEnded) {
      Future.delayed(const Duration(milliseconds: 1000), () {
        final diceValue = rollDice();
        _makeBotMove(diceValue);
      });
    }
  }

  // End game
  void endGame() {
    state = null;
  }

  // Pause game
  void pauseGame() {
    if (state == null) return;
    // Implementation for pausing game
  }

  // Resume game
  void resumeGame() {
    if (state == null) return;
    // Implementation for resuming game
  }

  // Add spectator
  void addSpectator(Player spectator) {
    if (state == null) return;

    final updatedSpectators = [...state!.spectators, spectator];
    state = state!.copyWith(spectators: updatedSpectators);
  }

  // Remove spectator
  void removeSpectator(String spectatorId) {
    if (state == null) return;

    final updatedSpectators =
        state!.spectators.where((s) => s.id != spectatorId).toList();
    state = state!.copyWith(spectators: updatedSpectators);
  }

  // Get game statistics
  Map<String, dynamic> getGameStats() {
    if (state == null) return {};

    return {
      'total_moves': state!.moves.length,
      'game_duration':
          state!.lastMoveTime != null
              ? DateTime.now().difference(state!.lastMoveTime!).inMinutes
              : 0,
      'current_player': state!.currentPlayer.name,
      'players_count': state!.players.length,
      'spectators_count': state!.spectators.length,
    };
  }
}
