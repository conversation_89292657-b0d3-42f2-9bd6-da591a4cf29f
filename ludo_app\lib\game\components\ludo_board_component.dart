import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';

class LudoBoardComponent extends Component {
  static const double boardSize = 600.0;
  static const double cellSize = boardSize / 15;

  late RectangleComponent _boardBackground;
  final List<RectangleComponent> _cells = [];
  final List<RectangleComponent> _homeAreas = [];
  final List<RectangleComponent> _safeZones = [];
  late Component _centerTriangles;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Create board background
    _createBoardBackground();

    // Create grid cells
    _createGridCells();

    // Create home areas
    _createHomeAreas();

    // Create safe zones
    _createSafeZones();

    // Create center triangles
    _createCenterTriangles();

    // Create board path
    _createBoardPath();
  }

  void _createBoardBackground() {
    _boardBackground = RectangleComponent(
      size: Vector2(boardSize, boardSize),
      paint: Paint()..color = Colors.white,
    );
    add(_boardBackground);

    // Add border
    final border = RectangleComponent(
      size: Vector2(boardSize, boardSize),
      paint:
          Paint()
            ..color = Colors.transparent
            ..style = PaintingStyle.stroke
            ..strokeWidth = 3,
    );
    border.paint.color = Colors.grey.shade400;
    add(border);
  }

  void _createGridCells() {
    for (int row = 0; row < 15; row++) {
      for (int col = 0; col < 15; col++) {
        final cell = RectangleComponent(
          position: Vector2(col * cellSize, row * cellSize),
          size: Vector2(cellSize, cellSize),
          paint:
              Paint()
                ..color = Colors.transparent
                ..style = PaintingStyle.stroke
                ..strokeWidth = 0.5,
        );
        cell.paint.color = Colors.grey.shade300;
        _cells.add(cell);
        add(cell);
      }
    }
  }

  void _createHomeAreas() {
    final homeConfigs = [
      // Red home (top-left)
      {
        'position': Vector2(0, 0),
        'size': Vector2(cellSize * 6, cellSize * 6),
        'color': AppTheme.primaryRed.withValues(alpha: 0.2),
      },
      // Blue home (top-right)
      {
        'position': Vector2(cellSize * 9, 0),
        'size': Vector2(cellSize * 6, cellSize * 6),
        'color': AppTheme.primaryBlue.withValues(alpha: 0.2),
      },
      // Green home (bottom-left)
      {
        'position': Vector2(0, cellSize * 9),
        'size': Vector2(cellSize * 6, cellSize * 6),
        'color': AppTheme.primaryGreen.withValues(alpha: 0.2),
      },
      // Yellow home (bottom-right)
      {
        'position': Vector2(cellSize * 9, cellSize * 9),
        'size': Vector2(cellSize * 6, cellSize * 6),
        'color': AppTheme.primaryYellow.withValues(alpha: 0.2),
      },
    ];

    for (final config in homeConfigs) {
      final homeArea = RectangleComponent(
        position: config['position'] as Vector2,
        size: config['size'] as Vector2,
        paint: Paint()..color = config['color'] as Color,
      );
      _homeAreas.add(homeArea);
      add(homeArea);

      // Add border to home area
      final border = RectangleComponent(
        position: config['position'] as Vector2,
        size: config['size'] as Vector2,
        paint:
            Paint()
              ..color = (config['color'] as Color).withValues(alpha: 0.8)
              ..style = PaintingStyle.stroke
              ..strokeWidth = 2,
      );
      add(border);
    }
  }

  void _createSafeZones() {
    final safeZoneConfigs = [
      // Red safe zone (vertical column)
      {
        'position': Vector2(cellSize, cellSize * 6),
        'size': Vector2(cellSize, cellSize * 3),
        'color': AppTheme.primaryRed.withValues(alpha: 0.3),
      },
      // Blue safe zone (horizontal row)
      {
        'position': Vector2(cellSize * 6, cellSize),
        'size': Vector2(cellSize * 3, cellSize),
        'color': AppTheme.primaryBlue.withValues(alpha: 0.3),
      },
      // Green safe zone (vertical column)
      {
        'position': Vector2(cellSize * 13, cellSize * 6),
        'size': Vector2(cellSize, cellSize * 3),
        'color': AppTheme.primaryGreen.withValues(alpha: 0.3),
      },
      // Yellow safe zone (horizontal row)
      {
        'position': Vector2(cellSize * 6, cellSize * 13),
        'size': Vector2(cellSize * 3, cellSize),
        'color': AppTheme.primaryYellow.withValues(alpha: 0.3),
      },
    ];

    for (final config in safeZoneConfigs) {
      final safeZone = RectangleComponent(
        position: config['position'] as Vector2,
        size: config['size'] as Vector2,
        paint: Paint()..color = config['color'] as Color,
      );
      _safeZones.add(safeZone);
      add(safeZone);
    }
  }

  void _createCenterTriangles() {
    _centerTriangles = Component();
    add(_centerTriangles);

    final centerX = boardSize / 2;
    final centerY = boardSize / 2;
    final triangleSize = cellSize * 1.5;

    // Red triangle (top)
    final redTriangle = _createTriangle(
      Vector2(centerX, centerY - triangleSize / 2),
      triangleSize,
      AppTheme.primaryRed,
      0, // pointing up
    );
    _centerTriangles.add(redTriangle);

    // Blue triangle (right)
    final blueTriangle = _createTriangle(
      Vector2(centerX + triangleSize / 2, centerY),
      triangleSize,
      AppTheme.primaryBlue,
      90, // pointing right
    );
    _centerTriangles.add(blueTriangle);

    // Green triangle (bottom)
    final greenTriangle = _createTriangle(
      Vector2(centerX, centerY + triangleSize / 2),
      triangleSize,
      AppTheme.primaryGreen,
      180, // pointing down
    );
    _centerTriangles.add(greenTriangle);

    // Yellow triangle (left)
    final yellowTriangle = _createTriangle(
      Vector2(centerX - triangleSize / 2, centerY),
      triangleSize,
      AppTheme.primaryYellow,
      270, // pointing left
    );
    _centerTriangles.add(yellowTriangle);

    // Center circle
    final centerCircle = CircleComponent(
      radius: cellSize * 0.4,
      position: Vector2(centerX - cellSize * 0.4, centerY - cellSize * 0.4),
      paint: Paint()..color = Colors.white,
    );
    _centerTriangles.add(centerCircle);

    // Center circle border
    final centerBorder = CircleComponent(
      radius: cellSize * 0.4,
      position: Vector2(centerX - cellSize * 0.4, centerY - cellSize * 0.4),
      paint:
          Paint()
            ..color = Colors.grey.shade400
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2,
    );
    _centerTriangles.add(centerBorder);
  }

  Component _createTriangle(
    Vector2 position,
    double size,
    Color color,
    double rotation,
  ) {
    final triangle = PolygonComponent(
      [
        Vector2(0, -size / 2),
        Vector2(-size / 2, size / 2),
        Vector2(size / 2, size / 2),
      ],
      position: position,
      paint: Paint()..color = color,
      angle: rotation * (3.14159 / 180), // Convert degrees to radians
    );
    return triangle;
  }

  void _createBoardPath() {
    // Create the main playing path around the board
    final pathCells = _generatePathCells();

    for (int i = 0; i < pathCells.length; i++) {
      final cell = pathCells[i];
      final pathCell = RectangleComponent(
        position: cell,
        size: Vector2(cellSize, cellSize),
        paint: Paint()..color = Colors.grey.shade100,
      );
      add(pathCell);

      // Add border to path cells
      final border = RectangleComponent(
        position: cell,
        size: Vector2(cellSize, cellSize),
        paint:
            Paint()
              ..color = Colors.grey.shade400
              ..style = PaintingStyle.stroke
              ..strokeWidth = 1,
      );
      add(border);

      // Mark safe positions
      if (_isSafePosition(i)) {
        final safeMarker = CircleComponent(
          radius: cellSize * 0.15,
          position: Vector2(
            cell.x + cellSize / 2 - cellSize * 0.15,
            cell.y + cellSize / 2 - cellSize * 0.15,
          ),
          paint: Paint()..color = Colors.green.shade600,
        );
        add(safeMarker);
      }
    }
  }

  List<Vector2> _generatePathCells() {
    final path = <Vector2>[];

    // Bottom row (positions 0-5) - Red starting area
    for (int i = 1; i <= 5; i++) {
      path.add(Vector2(cellSize * i, cellSize * 8));
    }

    // Right column going up (positions 6-11)
    for (int i = 7; i >= 2; i--) {
      path.add(Vector2(cellSize * 6, cellSize * i));
    }

    // Top row going right (positions 12-17)
    for (int i = 7; i <= 12; i++) {
      path.add(Vector2(cellSize * i, cellSize * 6));
    }

    // Far right column going down (positions 18-23)
    for (int i = 7; i <= 12; i++) {
      path.add(Vector2(cellSize * 13, cellSize * i));
    }

    // Bottom row going left (positions 24-29)
    for (int i = 12; i >= 7; i--) {
      path.add(Vector2(cellSize * i, cellSize * 13));
    }

    // Left column going up (positions 30-35)
    for (int i = 12; i >= 7; i--) {
      path.add(Vector2(cellSize * 6, cellSize * i));
    }

    // Top row going left (positions 36-41)
    for (int i = 5; i >= 0; i--) {
      path.add(Vector2(cellSize * i, cellSize * 6));
    }

    // Far left column going down (positions 42-47)
    for (int i = 7; i <= 12; i++) {
      path.add(Vector2(cellSize * 0, cellSize * i));
    }

    // Bottom row going right (positions 48-51)
    for (int i = 1; i <= 4; i++) {
      path.add(Vector2(cellSize * i, cellSize * 13));
    }

    return path;
  }

  bool _isSafePosition(int position) {
    // Safe positions in LUDO (typically every 13th position and starting positions)
    const safePositions = {0, 8, 13, 21, 26, 34, 39, 47};
    return safePositions.contains(position);
  }
}
