{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/android/sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/android/sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/android/sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/android/sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-97bb8aabd4ddc1f9c6cc.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-14424e6f8b5b67d3d853.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-783cd2ef8459cac4c4a1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-14424e6f8b5b67d3d853.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-783cd2ef8459cac4c4a1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-97bb8aabd4ddc1f9c6cc.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}