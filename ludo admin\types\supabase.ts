export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      admin_users: {
        Row: {
          id: string
          email: string
          role: string
          created_at: string
          last_login: string | null
        }
        Insert: {
          id: string
          email: string
          role?: string
          created_at?: string
          last_login?: string | null
        }
        Update: {
          id?: string
          email?: string
          role?: string
          created_at?: string
          last_login?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "admin_users_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          username: string
          email: string
          is_verified: boolean
          is_blocked: boolean
          created_at: string
          last_login: string | null
          avatar_url: string | null
          games_played: number
          games_won: number
          total_earnings: number
          wallet_balance: number
        }
        Insert: {
          id?: string
          username: string
          email: string
          is_verified?: boolean
          is_blocked?: boolean
          created_at?: string
          last_login?: string | null
          avatar_url?: string | null
          games_played?: number
          games_won?: number
          total_earnings?: number
          wallet_balance?: number
        }
        Update: {
          id?: string
          username?: string
          email?: string
          is_verified?: boolean
          is_blocked?: boolean
          created_at?: string
          last_login?: string | null
          avatar_url?: string | null
          games_played?: number
          games_won?: number
          total_earnings?: number
          wallet_balance?: number
        }
      }
      game_rooms: {
        Row: {
          id: string
          name: string
          status: "waiting" | "active" | "completed" | "terminated"
          player_limit: number
          entry_fee: number
          reward_amount: number
          created_at: string
          ended_at: string | null
        }
        Insert: {
          id?: string
          name: string
          status?: "waiting" | "active" | "completed" | "terminated"
          player_limit?: number
          entry_fee?: number
          reward_amount?: number
          created_at?: string
          ended_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          status?: "waiting" | "active" | "completed" | "terminated"
          player_limit?: number
          entry_fee?: number
          reward_amount?: number
          created_at?: string
          ended_at?: string | null
        }
      }
      game_participants: {
        Row: {
          id: string
          game_id: string
          user_id: string
          position: number
          score: number
          is_winner: boolean
          joined_at: string
        }
        Insert: {
          id?: string
          game_id: string
          user_id: string
          position?: number
          score?: number
          is_winner?: boolean
          joined_at?: string
        }
        Update: {
          id?: string
          game_id?: string
          user_id?: string
          position?: number
          score?: number
          is_winner?: boolean
          joined_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "game_participants_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "game_rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_participants_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      audit_logs: {
        Row: {
          id: string
          admin_id: string
          action: string
          entity_type: string
          entity_id: string
          details: Json
          created_at: string
          ip_address: string
        }
        Insert: {
          id?: string
          admin_id: string
          action: string
          entity_type: string
          entity_id: string
          details?: Json
          created_at?: string
          ip_address?: string
        }
        Update: {
          id?: string
          admin_id?: string
          action?: string
          entity_type?: string
          entity_id?: string
          details?: Json
          created_at?: string
          ip_address?: string
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_admin_id_fkey"
            columns: ["admin_id"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          }
        ]
      }
      transactions: {
        Row: {
          id: string
          user_id: string
          type: "deposit" | "withdrawal" | "game_entry" | "game_reward" | "refund"
          amount: number
          status: "pending" | "completed" | "failed" | "cancelled"
          description: string | null
          reference_id: string | null
          created_at: string
          processed_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          type: "deposit" | "withdrawal" | "game_entry" | "game_reward" | "refund"
          amount: number
          status?: "pending" | "completed" | "failed" | "cancelled"
          description?: string | null
          reference_id?: string | null
          created_at?: string
          processed_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          type?: "deposit" | "withdrawal" | "game_entry" | "game_reward" | "refund"
          amount?: number
          status?: "pending" | "completed" | "failed" | "cancelled"
          description?: string | null
          reference_id?: string | null
          created_at?: string
          processed_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      game_moves: {
        Row: {
          id: string
          game_id: string
          user_id: string
          move_number: number
          dice_value: number | null
          piece_moved: number | null
          from_position: number | null
          to_position: number | null
          move_type: "normal" | "capture" | "home" | "safe" | null
          created_at: string
        }
        Insert: {
          id?: string
          game_id: string
          user_id: string
          move_number: number
          dice_value?: number | null
          piece_moved?: number | null
          from_position?: number | null
          to_position?: number | null
          move_type?: "normal" | "capture" | "home" | "safe" | null
          created_at?: string
        }
        Update: {
          id?: string
          game_id?: string
          user_id?: string
          move_number?: number
          dice_value?: number | null
          piece_moved?: number | null
          from_position?: number | null
          to_position?: number | null
          move_type?: "normal" | "capture" | "home" | "safe" | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "game_moves_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "game_rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_moves_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_profiles: {
        Row: {
          id: string
          username: string
          email: string
          avatar_url: string | null
          created_at: string
          games_played: number
          games_won: number
          is_verified: boolean
          is_blocked: boolean
          total_earnings: number
          wallet_balance: number
        }
        // Insert: {
        //   id?: string
        //   username: string
        //   email: string
        //   avatar_url?: string | null
        //   created_at?: string
        //   games_played?: number
        //   games_won?: number
        //   is_verified?: boolean
        //   is_blocked?: boolean
        //   total_earnings?: number
        //   wallet_balance?: number
        // }
        // Update: {
        //   id?: string
        //   username?: string
        //   email?: string
        //   avatar_url?: string | null
        //   created_at?: string
        //   games_played?: number
        //   games_won?: number
        //   is_verified?: boolean
        //   is_blocked?: boolean
        //   total_earnings?: number
        //   wallet_balance?: number
        // }
      }
    }
  }
}