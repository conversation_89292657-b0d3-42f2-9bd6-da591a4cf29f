import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';

// Auth state provider
final authStateProvider = StreamProvider<AuthState>((ref) {
  return AuthService.authStateChanges;
});

// Current user provider
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.session?.user,
    loading: () => null,
    error: (_, __) => null,
  );
});

// User profile provider
final userProfileProvider = FutureProvider<UserModel?>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return null;

  return await AuthService.getUserProfile(user.id);
});

// Auth controller
final authControllerProvider =
    StateNotifierProvider<AuthController, AppAuthState>((ref) {
      return AuthController();
    });

class AuthController extends StateNotifier<AppAuthState> {
  AuthController() : super(const AppAuthState.initial());

  Future<void> signUp({
    required String email,
    required String password,
    required String username,
  }) async {
    state = const AppAuthState.loading();

    try {
      final response = await AuthService.signUp(
        email: email,
        password: password,
        username: username,
      );

      if (response.user != null) {
        state = AppAuthState.authenticated(response.user!);
      } else {
        state = const AppAuthState.error('Sign up failed');
      }
    } catch (e) {
      state = AppAuthState.error(e.toString());
    }
  }

  Future<void> signIn({required String email, required String password}) async {
    state = const AppAuthState.loading();

    try {
      final response = await AuthService.signIn(
        email: email,
        password: password,
      );

      if (response.user != null) {
        state = AppAuthState.authenticated(response.user!);
      } else {
        state = const AppAuthState.error('Sign in failed');
      }
    } catch (e) {
      state = AppAuthState.error(e.toString());
    }
  }

  Future<void> signOut() async {
    state = const AppAuthState.loading();

    try {
      await AuthService.signOut();
      state = const AppAuthState.unauthenticated();
    } catch (e) {
      state = AppAuthState.error(e.toString());
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await AuthService.resetPassword(email);
    } catch (e) {
      state = AppAuthState.error(e.toString());
    }
  }
}

// Auth state class
class AppAuthState {
  final bool isLoading;
  final bool isAuthenticated;
  final User? user;
  final String? error;

  const AppAuthState._({
    required this.isLoading,
    required this.isAuthenticated,
    this.user,
    this.error,
  });

  const AppAuthState.initial()
    : this._(isLoading: false, isAuthenticated: false);

  const AppAuthState.loading()
    : this._(isLoading: true, isAuthenticated: false);

  const AppAuthState.authenticated(User user)
    : this._(isLoading: false, isAuthenticated: true, user: user);

  const AppAuthState.unauthenticated()
    : this._(isLoading: false, isAuthenticated: false);

  const AppAuthState.error(String error)
    : this._(isLoading: false, isAuthenticated: false, error: error);
}
