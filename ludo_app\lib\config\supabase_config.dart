import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  static const String supabaseUrl = 'https://jgsdfuyqueiuwitndswc.supabase.co';
  // static String supabaseUrl =
  // dotenv.env['SUPABASE_URL'] ?? 'https://jgsdfuyqueiuwitndswc.supabase.co';
  // static String supabaseAnonKey =
  // dotenv.env['SUPABASE_ANON_KEY'] ??
  // 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Impnc2RmdXlxdWVpdXdpdG5kc3djIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxMjUzNzAsImV4cCI6MjA2NDcwMTM3MH0.Z7n6SU9s9V4jdGNCk8LLmAAiHVVfH9ptlERiOzVQuVA';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Impnc2RmdXlxdWVpdXdpdG5kc3djIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxMjUzNzAsImV4cCI6MjA2NDcwMTM3MH0.Z7n6SU9s9V4jdGNCk8LLmAAiHVVfH9ptlERiOzVQuVA';

  // For local development
  // static const String supabaseUrl = 'http://localhost:54321';
  // static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxvY2FsaG9zdCIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNjQ1MTkyODI0LCJleHAiOjE5NjA3Njg4MjR9.M9jrxyvPLkUxWgOYSf5dNdJ8v_eWrqwgWvKXpvQaUGI';

  static SupabaseClient get client => Supabase.instance.client;

  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      debug: true, // Set to false in production
    );
  }
}

// Database table names
class TableNames {
  static const String users = 'users';
  static const String gameRooms = 'game_rooms';
  static const String gameParticipants = 'game_participants';
  static const String gameMoves = 'game_moves';
  static const String transactions = 'transactions';
}

// Realtime channels
class RealtimeChannels {
  static const String gameRooms = 'game_rooms_channel';
  static const String gameParticipants = 'game_participants_channel';
  static const String gameMoves = 'game_moves_channel';
}
