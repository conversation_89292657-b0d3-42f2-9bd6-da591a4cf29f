import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });

  try {
    // Get the session
    const {
      data: { session },
      error: sessionError
    } = await supabase.auth.getSession();

    // If there's an error getting the session, redirect to login
    if (sessionError) {
      console.error('Session error:', sessionError);
      const url = req.nextUrl.clone();
      url.pathname = '/auth/login';
      url.searchParams.set('error', 'session_error');
      return NextResponse.redirect(url);
    }

    // If no session, redirect to login
    if (!session?.user) {
      const url = req.nextUrl.clone();
      url.pathname = '/auth/login';
      url.searchParams.set('redirectTo', req.nextUrl.pathname);
      return NextResponse.redirect(url);
    }

    // For dashboard routes, check admin role
    if (req.nextUrl.pathname.startsWith('/dashboard')) {
      try {
        const { data: adminUser, error: adminError } = await supabase
          .from('admin_users')
          .select('role, email')
          .eq('id', session.user.id)
          .single();

        // If there's an error fetching admin data or user is not an admin
        if (adminError || !adminUser) {
          console.error('Admin check error:', adminError || 'User not found in admin_users');
          const url = req.nextUrl.clone();
          url.pathname = '/auth/login';
          url.searchParams.set('error', 'unauthorized');
          url.searchParams.set('message', 'Admin access required');
          return NextResponse.redirect(url);
        }

        // Check if user has valid admin role
        const validRoles = ['admin', 'super_admin', 'moderator'];
        if (!validRoles.includes(adminUser.role)) {
          console.error('Invalid admin role:', adminUser.role);
          const url = req.nextUrl.clone();
          url.pathname = '/auth/login';
          url.searchParams.set('error', 'insufficient_permissions');
          url.searchParams.set('message', 'Insufficient permissions');
          return NextResponse.redirect(url);
        }

        // Add admin info to response headers for use in components
        res.headers.set('x-admin-role', adminUser.role);
        res.headers.set('x-admin-email', adminUser.email);

      } catch (error) {
        console.error('Middleware admin check error:', error);
        const url = req.nextUrl.clone();
        url.pathname = '/auth/login';
        url.searchParams.set('error', 'server_error');
        return NextResponse.redirect(url);
      }
    }

    return res;

  } catch (error) {
    console.error('Middleware error:', error);
    const url = req.nextUrl.clone();
    url.pathname = '/auth/login';
    url.searchParams.set('error', 'middleware_error');
    return NextResponse.redirect(url);
  }
}

export const config = {
  matcher: ['/dashboard/:path*'],
};