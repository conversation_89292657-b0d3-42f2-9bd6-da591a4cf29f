import 'package:flutter/material.dart';
import 'package:flame/game.dart';
import '../models/game_state_model.dart';
import '../game/flame_ludo_game.dart';

class GameBoardWidget extends StatefulWidget {
  final GameStateModel gameState;
  final Function(int)? onPieceMove;
  final bool isSpectator;

  const GameBoardWidget({
    super.key,
    required this.gameState,
    this.onPieceMove,
    this.isSpectator = false,
  });

  @override
  State<GameBoardWidget> createState() => _GameBoardWidgetState();
}

class _GameBoardWidgetState extends State<GameBoardWidget> {
  late FlameLudoGame _game;

  @override
  void initState() {
    super.initState();
    _game = FlameLudoGame();
    _game.initialize(
      gameState: widget.gameState,
      onPieceMove: widget.onPieceMove ?? (_) {},
      isSpectator: widget.isSpectator,
    );
  }

  @override
  void didUpdateWidget(GameBoardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.gameState != widget.gameState) {
      _game.updateGameState(widget.gameState);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.0,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: GameWidget<FlameLudoGame>.controlled(gameFactory: () => _game),
        ),
      ),
    );
  }
}
