#!/usr/bin/env node

/**
 * Database Test Script for Ludo Admin
 * 
 * This script tests all database operations to ensure everything is working correctly.
 * 
 * Usage: node scripts/test-database.js
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDatabaseConnection() {
  console.log('🔗 Testing database connection...');
  
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      throw error;
    }
    
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

async function testTableExists(tableName) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
    
    if (error && error.code === 'PGRST116') {
      return false; // Table doesn't exist
    }
    
    return true;
  } catch (error) {
    return false;
  }
}

async function testTables() {
  console.log('\n📋 Testing database tables...');
  
  const requiredTables = [
    'users',
    'admin_users', 
    'game_rooms',
    'game_participants',
    'audit_logs',
    'transactions',
    'game_moves',
    'user_profiles'
  ];
  
  const results = {};
  
  for (const table of requiredTables) {
    const exists = await testTableExists(table);
    results[table] = exists;
    
    if (exists) {
      console.log(`   ✅ ${table}`);
    } else {
      console.log(`   ❌ ${table} (missing)`);
    }
  }
  
  return results;
}

async function testDataQueries() {
  console.log('\n📊 Testing data queries...');
  
  try {
    // Test users query
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, email, games_played, games_won')
      .limit(5);
    
    if (usersError) {
      console.log(`   ⚠️  Users query: ${usersError.message}`);
    } else {
      console.log(`   ✅ Users query: ${users?.length || 0} records`);
    }
    
    // Test game rooms query
    const { data: games, error: gamesError } = await supabase
      .from('game_rooms')
      .select('id, name, status, player_limit')
      .limit(5);
    
    if (gamesError) {
      console.log(`   ⚠️  Game rooms query: ${gamesError.message}`);
    } else {
      console.log(`   ✅ Game rooms query: ${games?.length || 0} records`);
    }
    
    // Test admin users query
    const { data: admins, error: adminsError } = await supabase
      .from('admin_users')
      .select('id, email, role')
      .limit(5);
    
    if (adminsError) {
      console.log(`   ⚠️  Admin users query: ${adminsError.message}`);
    } else {
      console.log(`   ✅ Admin users query: ${admins?.length || 0} records`);
    }
    
    // Test user_profiles view
    const { data: profiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('id, username, games_played')
      .limit(5);
    
    if (profilesError) {
      console.log(`   ⚠️  User profiles view: ${profilesError.message}`);
    } else {
      console.log(`   ✅ User profiles view: ${profiles?.length || 0} records`);
    }
    
  } catch (error) {
    console.error('❌ Error testing queries:', error.message);
  }
}

async function testAuthentication() {
  console.log('\n🔐 Testing authentication...');
  
  try {
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.log(`   ⚠️  Auth session: ${error.message}`);
    } else {
      console.log('   ✅ Auth system accessible');
    }
    
  } catch (error) {
    console.error('❌ Error testing authentication:', error.message);
  }
}

async function generateReport() {
  console.log('\n📈 Generating database report...');
  
  try {
    // Count records in each table
    const tables = ['users', 'admin_users', 'game_rooms', 'game_participants', 'audit_logs', 'transactions'];
    const counts = {};
    
    for (const table of tables) {
      try {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        counts[table] = error ? 'Error' : count || 0;
      } catch (error) {
        counts[table] = 'Error';
      }
    }
    
    console.log('\n📊 Database Summary:');
    console.log('   ┌─────────────────────┬─────────┐');
    console.log('   │ Table               │ Records │');
    console.log('   ├─────────────────────┼─────────┤');
    
    for (const [table, count] of Object.entries(counts)) {
      const paddedTable = table.padEnd(19);
      const paddedCount = count.toString().padStart(7);
      console.log(`   │ ${paddedTable} │ ${paddedCount} │`);
    }
    
    console.log('   └─────────────────────┴─────────┘');
    
  } catch (error) {
    console.error('❌ Error generating report:', error.message);
  }
}

async function runTests() {
  console.log('🧪 Starting database tests...\n');
  
  // Test connection
  const connected = await testDatabaseConnection();
  if (!connected) {
    console.log('\n❌ Cannot proceed without database connection.');
    process.exit(1);
  }
  
  // Test tables
  const tableResults = await testTables();
  
  // Test queries
  await testDataQueries();
  
  // Test authentication
  await testAuthentication();
  
  // Generate report
  await generateReport();
  
  // Summary
  const missingTables = Object.entries(tableResults)
    .filter(([table, exists]) => !exists)
    .map(([table]) => table);
  
  console.log('\n🎯 Test Summary:');
  
  if (missingTables.length === 0) {
    console.log('   ✅ All tables exist and are accessible');
    console.log('   ✅ Database is ready for use');
    console.log('\n🚀 You can now start the application with: npm run dev');
  } else {
    console.log(`   ⚠️  Missing tables: ${missingTables.join(', ')}`);
    console.log('   📝 Please run the database setup first');
    console.log('\n🔧 Setup instructions:');
    console.log('   1. Copy SQL from supabase/migrations/*.sql');
    console.log('   2. Run in Supabase SQL Editor');
    console.log('   3. Copy SQL from supabase/seed.sql');
    console.log('   4. Run in Supabase SQL Editor');
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
