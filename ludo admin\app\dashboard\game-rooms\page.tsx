"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter,
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Pagination } from '@/components/dashboard/pagination';
import { formatDistanceToNow, format } from 'date-fns';
import { 
  MoreHorizontalIcon, 
  SearchIcon, 
  FilterIcon,
  StopCircleIcon,
  PlayCircleIcon,
  EyeIcon,
  RefreshCwIcon,
  LoaderCircleIcon
} from 'lucide-react';

interface GameRoom {
  id: string;
  created_at: string;
  status: string;
  player_limit: number;
  entry_fee: number;
  reward: number;
  creator_id: string;
  completed_at: string | null;
  game_type: string;
}

export default function GameRoomsPage() {
  const [gameRooms, setGameRooms] = useState<GameRoom[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRooms, setTotalRooms] = useState(0);
  const [pageSize] = useState(10);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [activeTab, setActiveTab] = useState<string>("active");
  const [selectedRoom, setSelectedRoom] = useState<GameRoom | null>(null);
  const [actionConfirmOpen, setActionConfirmOpen] = useState(false);
  const [actionType, setActionType] = useState<'terminate' | 'pause' | 'resume' | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const router = useRouter();
  const { toast } = useToast();
  const supabase = createClient();

  const fetchGameRooms = async (page: number, status: string, search: string) => {
    try {
      setIsLoading(true);
      
      // Calculate pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      
      // Base query
      let query = supabase
        .from('game_rooms')
        .select('*', { count: 'exact' });
      
      // Filter by status if not "all"
      if (status !== "all") {
        query = query.eq('status', status);
      }
      
      // Add search filter if provided
      if (search) {
        query = query.or(`id.ilike.%${search}%,game_type.ilike.%${search}%`);
      }
      
      // Add pagination and sort by created_at
      query = query.range(from, to).order('created_at', { ascending: false });
      
      const { data, count, error } = await query;
      
      if (error) throw error;
      
      setGameRooms(data || []);
      setTotalRooms(count || 0);
      setTotalPages(Math.ceil((count || 0) / pageSize));
    } catch (error) {
      console.error('Error fetching game rooms:', error);
      toast({
        title: "Error loading game rooms",
        description: "Please try refreshing the page",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Map tab to status filter
    const statusMap: Record<string, string> = {
      active: "active",
      waiting: "waiting",
      completed: "completed",
      all: "all"
    };
    
    setStatusFilter(statusMap[activeTab] || "all");
    setCurrentPage(1); // Reset to first page on tab change
  }, [activeTab]);

  useEffect(() => {
    fetchGameRooms(currentPage, statusFilter, searchQuery);
  }, [currentPage, statusFilter, supabase]);

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page on new search
    fetchGameRooms(1, statusFilter, searchQuery);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const confirmAction = (room: GameRoom, action: 'terminate' | 'pause' | 'resume') => {
    setSelectedRoom(room);
    setActionType(action);
    setActionConfirmOpen(true);
  };
  
  const performAction = async () => {
    if (!selectedRoom || !actionType) return;
    
    setIsProcessing(true);
    
    try {
      switch (actionType) {
        case 'terminate':
          await terminateGame(selectedRoom.id);
          break;
        case 'pause':
          await pauseGame(selectedRoom.id);
          break;
        case 'resume':
          await resumeGame(selectedRoom.id);
          break;
      }
    } catch (error) {
      console.error(`Error performing ${actionType} action:`, error);
      toast({
        title: `Error: ${actionType} failed`,
        description: "Please try again",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
      setActionConfirmOpen(false);
      setSelectedRoom(null);
      setActionType(null);
    }
  };
  
  const terminateGame = async (gameId: string) => {
    const { error } = await supabase
      .from('game_rooms')
      .update({ 
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('id', gameId);
      
    if (error) throw error;
    
    // Log admin action
    await logAdminAction('terminate', 'game', gameId);
    
    toast({
      title: "Game terminated",
      description: "The game has been terminated and marked as completed",
    });
    
    // Refresh game rooms list
    fetchGameRooms(currentPage, statusFilter, searchQuery);
  };
  
  const pauseGame = async (gameId: string) => {
    const { error } = await supabase
      .from('game_rooms')
      .update({ status: 'paused' })
      .eq('id', gameId);
      
    if (error) throw error;
    
    // Log admin action
    await logAdminAction('pause', 'game', gameId);
    
    toast({
      title: "Game paused",
      description: "The game has been paused successfully",
    });
    
    // Refresh game rooms list
    fetchGameRooms(currentPage, statusFilter, searchQuery);
  };
  
  const resumeGame = async (gameId: string) => {
    const { error } = await supabase
      .from('game_rooms')
      .update({ status: 'active' })
      .eq('id', gameId);
      
    if (error) throw error;
    
    // Log admin action
    await logAdminAction('resume', 'game', gameId);
    
    toast({
      title: "Game resumed",
      description: "The game has been resumed successfully",
    });
    
    // Refresh game rooms list
    fetchGameRooms(currentPage, statusFilter, searchQuery);
  };
  
  const logAdminAction = async (action: string, resourceType: string, resourceId: string) => {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return;
    
    await supabase.from('admin_audit_logs').insert({
      admin_id: user.id,
      action,
      resource_type: resourceType,
      resource_id: resourceId,
      details: {},
      ip_address: '127.0.0.1' // In a real implementation, this would be the actual IP
    });
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="outline" className="bg-green-500/10 text-green-700 border-green-500/20">Active</Badge>;
      case 'waiting':
        return <Badge variant="outline" className="bg-blue-500/10 text-blue-700 border-blue-500/20">Waiting</Badge>;
      case 'paused':
        return <Badge variant="outline" className="bg-amber-500/10 text-amber-700 border-amber-500/20">Paused</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-gray-500/10 text-gray-700 border-gray-500/20">Completed</Badge>;
      default:
        return <Badge variant="outline" className="bg-muted text-muted-foreground">Unknown</Badge>;
    }
  };

  const getActionDialogContent = () => {
    if (!selectedRoom || !actionType) return null;
    
    const actionTexts = {
      terminate: {
        title: "Terminate Game",
        description: `Are you sure you want to terminate game #${selectedRoom.id.slice(0, 8)}? This will end the game immediately and distribute rewards according to current standings.`,
        buttonText: "Terminate Game",
        buttonVariant: "destructive" as const
      },
      pause: {
        title: "Pause Game",
        description: `Are you sure you want to pause game #${selectedRoom.id.slice(0, 8)}? Players will not be able to make moves until you resume the game.`,
        buttonText: "Pause Game",
        buttonVariant: "default" as const
      },
      resume: {
        title: "Resume Game",
        description: `Are you sure you want to resume game #${selectedRoom.id.slice(0, 8)}? Players will be able to continue playing.`,
        buttonText: "Resume Game",
        buttonVariant: "default" as const
      },
    };
    
    const { title, description, buttonText, buttonVariant } = actionTexts[actionType];
    
    return (
      <>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter className="mt-4">
          <Button 
            variant="outline" 
            onClick={() => setActionConfirmOpen(false)}
            disabled={isProcessing}
          >
            Cancel
          </Button>
          <Button 
            variant={buttonVariant} 
            onClick={performAction}
            disabled={isProcessing}
          >
            {isProcessing && <LoaderCircleIcon className="mr-2 h-4 w-4 animate-spin" />}
            {buttonText}
          </Button>
        </DialogFooter>
      </>
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Game Rooms</h1>
        <p className="text-muted-foreground">
          Manage and monitor game rooms across the platform
        </p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Game Rooms</CardTitle>
          <CardDescription>
            Total rooms: {totalRooms}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="waiting">Waiting</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="all">All</TabsTrigger>
            </TabsList>
          </Tabs>
        
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-grow">
              <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by ID or game type..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-9"
              />
            </div>
            <Button 
              onClick={handleSearch}
              className="sm:w-auto w-full"
            >
              Search
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="sm:w-auto w-full">
                  <FilterIcon className="h-4 w-4 mr-2" />
                  Filter by Type
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuRadioGroup value="all">
                  <DropdownMenuRadioItem value="all">All Types</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="standard">Standard</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="quick">Quick Play</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="tournament">Tournament</DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Players</TableHead>
                  <TableHead className="text-right">Entry Fee</TableHead>
                  <TableHead className="text-right">Reward</TableHead>
                  <TableHead className="text-right">Created</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-8 w-8 rounded-full ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : gameRooms.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="h-24 text-center">
                      No game rooms found.
                    </TableCell>
                  </TableRow>
                ) : (
                  gameRooms.map((room) => (
                    <TableRow key={room.id}>
                      <TableCell className="font-medium">
                        {room.id.slice(0, 8)}...
                      </TableCell>
                      <TableCell>{room.game_type}</TableCell>
                      <TableCell>
                        {getStatusBadge(room.status)}
                      </TableCell>
                      <TableCell>
                        {`${room.player_limit} players`}
                      </TableCell>
                      <TableCell className="text-right">
                        ${room.entry_fee}
                      </TableCell>
                      <TableCell className="text-right">
                        ${room.reward}
                      </TableCell>
                      <TableCell className="text-right text-xs">
                        {formatDistanceToNow(new Date(room.created_at), { addSuffix: true })}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontalIcon className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => router.push(`/dashboard/game-rooms/${room.id}`)}
                            >
                              <EyeIcon className="mr-2 h-4 w-4" />
                              View details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {room.status === 'active' && (
                              <DropdownMenuItem
                                onClick={() => confirmAction(room, 'pause')}
                              >
                                <StopCircleIcon className="mr-2 h-4 w-4" />
                                Pause game
                              </DropdownMenuItem>
                            )}
                            {room.status === 'paused' && (
                              <DropdownMenuItem
                                onClick={() => confirmAction(room, 'resume')}
                              >
                                <PlayCircleIcon className="mr-2 h-4 w-4" />
                                Resume game
                              </DropdownMenuItem>
                            )}
                            {(room.status === 'active' || room.status === 'paused' || room.status === 'waiting') && (
                              <DropdownMenuItem
                                onClick={() => confirmAction(room, 'terminate')}
                                className="text-red-600"
                              >
                                <StopCircleIcon className="mr-2 h-4 w-4" />
                                Terminate game
                              </DropdownMenuItem>
                            )}
                            {room.status === 'completed' && (
                              <DropdownMenuItem>
                                <RefreshCwIcon className="mr-2 h-4 w-4" />
                                Recompute rewards
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </CardFooter>
      </Card>
      
      <Dialog open={actionConfirmOpen} onOpenChange={setActionConfirmOpen}>
        <DialogContent>
          {getActionDialogContent()}
        </DialogContent>
      </Dialog>
    </div>
  );
}