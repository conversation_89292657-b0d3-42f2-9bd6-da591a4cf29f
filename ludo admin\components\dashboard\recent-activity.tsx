"use client";

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDistanceToNow } from 'date-fns';
import { 
  UserIcon, 
  GamepadIcon, 
  TrophyIcon, 
  AlertTriangleIcon,
  CircleCheckIcon,
  DollarSignIcon
} from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'user_signup' | 'game_completed' | 'game_started' | 'user_blocked' | 'transaction';
  user: {
    username: string;
    email: string;
  };
  description: string;
  timestamp: string;
  status: 'success' | 'warning' | 'info';
  metadata?: any;
}

export function RecentActivity() {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    const fetchRecentActivity = async () => {
      try {
        setIsLoading(true);
        
        // Fetch recent audit logs
        const { data: auditLogs, error: auditError } = await supabase
          .from('audit_logs')
          .select(`
            id,
            action,
            entity_type,
            entity_id,
            details,
            created_at,
            admin_users!inner(email)
          `)
          .order('created_at', { ascending: false })
          .limit(10);

        if (auditError) {
          console.error('Error fetching audit logs:', auditError);
        }

        // Fetch recent user signups
        const { data: recentUsers, error: usersError } = await supabase
          .from('users')
          .select('id, username, email, created_at')
          .order('created_at', { ascending: false })
          .limit(5);

        if (usersError) {
          console.error('Error fetching recent users:', usersError);
        }

        // Fetch recent game completions
        const { data: recentGames, error: gamesError } = await supabase
          .from('game_rooms')
          .select(`
            id,
            name,
            status,
            ended_at,
            users!game_rooms_created_by_fkey(username, email)
          `)
          .eq('status', 'completed')
          .order('ended_at', { ascending: false })
          .limit(5);

        if (gamesError) {
          console.error('Error fetching recent games:', gamesError);
        }

        // Combine and format activities
        const combinedActivities: ActivityItem[] = [];

        // Add user signups
        recentUsers?.forEach(user => {
          combinedActivities.push({
            id: `user_${user.id}`,
            type: 'user_signup',
            user: {
              username: user.username,
              email: user.email
            },
            description: 'signed up for the platform',
            timestamp: user.created_at,
            status: 'success'
          });
        });

        // Add game completions
        recentGames?.forEach(game => {
          if (game.users) {
            combinedActivities.push({
              id: `game_${game.id}`,
              type: 'game_completed',
              user: {
                username: game.users.username,
                email: game.users.email
              },
              description: `completed game "${game.name}"`,
              timestamp: game.ended_at || game.created_at,
              status: 'info'
            });
          }
        });

        // Add audit log activities
        auditLogs?.forEach(log => {
          combinedActivities.push({
            id: `audit_${log.id}`,
            type: log.action.toLowerCase().includes('block') ? 'user_blocked' : 'transaction',
            user: {
              username: 'Admin',
              email: log.admin_users.email
            },
            description: `${log.action.toLowerCase()} ${log.entity_type} ${log.entity_id}`,
            timestamp: log.created_at,
            status: log.action.toLowerCase().includes('block') ? 'warning' : 'info'
          });
        });

        // Sort by timestamp and take the most recent 8
        const sortedActivities = combinedActivities
          .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
          .slice(0, 8);

        setActivities(sortedActivities);
      } catch (error) {
        console.error('Error fetching recent activity:', error);
        
        // Set mock data for demonstration
        const mockActivities: ActivityItem[] = [
          {
            id: '1',
            type: 'user_signup',
            user: { username: 'player123', email: '<EMAIL>' },
            description: 'signed up for the platform',
            timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
            status: 'success'
          },
          {
            id: '2',
            type: 'game_completed',
            user: { username: 'ludoking', email: '<EMAIL>' },
            description: 'completed game "Quick Match #5"',
            timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
            status: 'info'
          },
          {
            id: '3',
            type: 'user_blocked',
            user: { username: 'Admin', email: '<EMAIL>' },
            description: 'blocked user for suspicious activity',
            timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
            status: 'warning'
          }
        ];
        setActivities(mockActivities);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchRecentActivity();
    
    // Set up real-time subscription for audit logs
    const subscription = supabase
      .channel('audit_logs_changes')
      .on('postgres_changes', 
        { event: 'INSERT', schema: 'public', table: 'audit_logs' }, 
        () => fetchRecentActivity()
      )
      .subscribe();
      
    return () => {
      supabase.removeChannel(subscription);
    };
  }, [supabase]);

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'user_signup':
        return <UserIcon className="h-4 w-4" />;
      case 'game_completed':
        return <TrophyIcon className="h-4 w-4" />;
      case 'game_started':
        return <GamepadIcon className="h-4 w-4" />;
      case 'user_blocked':
        return <AlertTriangleIcon className="h-4 w-4" />;
      case 'transaction':
        return <DollarSignIcon className="h-4 w-4" />;
      default:
        return <CircleCheckIcon className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: ActivityItem['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-500';
      case 'warning':
        return 'text-amber-500';
      case 'info':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-1 flex-1">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {activities.length === 0 ? (
        <p className="text-sm text-muted-foreground text-center py-4">No recent activity found</p>
      ) : (
        activities.map((activity) => (
          <div key={activity.id} className="flex items-center gap-3">
            <Avatar>
              <AvatarFallback>
                {activity.user.username.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 space-y-1">
              <p className="text-sm font-medium leading-none">
                {activity.user.username}
              </p>
              <p className="text-xs text-muted-foreground">
                {activity.description}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <div className={getStatusColor(activity.status)}>
                {getActivityIcon(activity.type)}
              </div>
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
              </span>
            </div>
          </div>
        ))
      )}
    </div>
  );
}
