import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/auth_provider.dart';
import '../screens/splash_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/home/<USER>';
import '../screens/game/game_setup_screen.dart';
import '../screens/game/game_board_screen.dart';
import '../screens/game/game_rooms_screen.dart';
import '../screens/profile/profile_screen.dart';

// App router provider
final appRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateProvider);

  return GoRouter(
    initialLocation: '/splash',
    redirect: (context, state) {
      return authState.when(
        data: (auth) {
          final isAuthenticated = auth.session != null;
          final isOnAuthPage = state.uri.path.startsWith('/auth');
          final isOnSplash = state.uri.path == '/splash';

          // If not authenticated and not on auth pages, redirect to login
          if (!isAuthenticated && !isOnAuthPage && !isOnSplash) {
            return '/auth/login';
          }

          // If authenticated and on auth pages, redirect to home
          if (isAuthenticated && isOnAuthPage) {
            return '/home';
          }

          // If authenticated and on splash, redirect to home
          if (isAuthenticated && isOnSplash) {
            return '/home';
          }

          return null; // No redirect needed
        },
        loading: () => null, // Stay on current page while loading
        error: (_, __) => '/auth/login', // Redirect to login on error
      );
    },
    routes: [
      // Splash screen
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Auth routes
      GoRoute(path: '/auth', redirect: (context, state) => '/auth/login'),
      GoRoute(
        path: '/auth/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/auth/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),

      // Main app routes
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),

      // Game routes
      GoRoute(
        path: '/game/setup',
        name: 'game-setup',
        builder: (context, state) {
          final mode = state.uri.queryParameters['mode'] ?? 'local';
          return GameSetupScreen(mode: mode);
        },
      ),
      GoRoute(
        path: '/game/rooms',
        name: 'game-rooms',
        builder: (context, state) => const GameRoomsScreen(),
      ),
      GoRoute(
        path: '/game/board/:gameId',
        name: 'game-board',
        builder: (context, state) {
          final gameId = state.pathParameters['gameId']!;
          final isSpectator = state.uri.queryParameters['spectator'] == 'true';
          return GameBoardScreen(gameId: gameId, isSpectator: isSpectator);
        },
      ),

      // Profile routes
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
      ),
    ],
    errorBuilder:
        (context, state) => Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'Page Not Found',
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'The page you are looking for does not exist.',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => context.go('/home'),
                  child: const Text('Go Home'),
                ),
              ],
            ),
          ),
        ),
  );
});

// Navigation helper extension
extension AppRouterExtension on GoRouter {
  void pushAndClearStack(String location) {
    while (canPop()) {
      pop();
    }
    pushReplacement(location);
  }
}

// Route names for easy access
class AppRoutes {
  static const String splash = '/splash';
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String home = '/home';
  static const String gameSetup = '/game/setup';
  static const String gameRooms = '/game/rooms';
  static const String gameBoard = '/game/board';
  static const String profile = '/profile';
}
