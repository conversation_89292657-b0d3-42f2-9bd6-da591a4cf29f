"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Overview } from "@/components/dashboard/overview";
import { RecentActivity } from "@/components/dashboard/recent-activity";
import { GameStatusCards } from "@/components/dashboard/game-status-cards";

interface DashboardStats {
  activeGames: number;
  totalUsers: number;
  completedGames: number;
  revenue: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    activeGames: 0,
    totalUsers: 0,
    completedGames: 0,
    revenue: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setIsLoading(true);

        // Fetch active games count
        const { count: activeGamesCount } = await supabase
          .from('game_rooms')
          .select('*', { count: 'exact', head: true })
          .in('status', ['active', 'waiting']);

        // Fetch total users count
        const { count: totalUsersCount } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });

        // Fetch completed games count
        const { count: completedGamesCount } = await supabase
          .from('game_rooms')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'completed');

        // Fetch revenue from completed games
        const { data: revenueData } = await supabase
          .from('game_rooms')
          .select('entry_fee')
          .eq('status', 'completed');

        const totalRevenue = revenueData?.reduce((sum, game) => sum + (game.entry_fee || 0), 0) || 0;

        setStats({
          activeGames: activeGamesCount || 0,
          totalUsers: totalUsersCount || 0,
          completedGames: completedGamesCount || 0,
          revenue: totalRevenue
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        // Set mock data for demonstration
        setStats({
          activeGames: 12,
          totalUsers: 1234,
          completedGames: 567,
          revenue: 12450
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardStats();
  }, [supabase]);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome to your Ludo game admin dashboard
        </p>
      </div>

      <GameStatusCards isLoading={isLoading} stats={stats} />

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Player Activity</CardTitle>
                <CardDescription>
                  Player signups and active sessions over time
                </CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <Overview />
              </CardContent>
            </Card>
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest events from the platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RecentActivity />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Advanced Analytics</CardTitle>
              <CardDescription>
                Detailed metrics and performance indicators
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center border rounded-md">
                <p className="text-muted-foreground">Advanced analytics charts will appear here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Reports</CardTitle>
              <CardDescription>
                Generate and download reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center border rounded-md">
                <p className="text-muted-foreground">Report generation tools will appear here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}