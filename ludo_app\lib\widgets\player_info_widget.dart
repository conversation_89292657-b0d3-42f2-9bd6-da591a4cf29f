import 'package:flutter/material.dart';
import '../models/game_state_model.dart';
import '../models/game_participant_model.dart';
import '../utils/app_theme.dart';

class PlayerInfoWidget extends StatelessWidget {
  final Player player;
  final bool isCurrentPlayer;
  final bool isSpectator;

  const PlayerInfoWidget({
    super.key,
    required this.player,
    required this.isCurrentPlayer,
    this.isSpectator = false,
  });

  @override
  Widget build(BuildContext context) {
    final playerColor = _getPlayerColor(player.color);
    final piecesAtHome = player.pieces.where((p) => p.hasReachedEnd).length;
    final piecesOnBoard =
        player.pieces.where((p) => p.position > -1 && !p.hasReachedEnd).length;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      padding: const EdgeInsets.all(8),
      width: 90,
      decoration: BoxDecoration(
        color:
            isCurrentPlayer
                ? playerColor.withValues(alpha: 0.1)
                : Colors.transparent,
        border: Border.all(
          color: isCurrentPlayer ? playerColor : Colors.grey.shade300,
          width: 1.5,
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          // Avatar
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: playerColor,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
            child: Center(
              child:
                  player.isBot
                      ? const Icon(
                        Icons.smart_toy,
                        color: Colors.white,
                        size: 18,
                      )
                      : Text(
                        player.name.isNotEmpty
                            ? player.name[0].toUpperCase()
                            : 'P',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
            ),
          ),

          const SizedBox(height: 4),

          // Name
          Text(
            player.name,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: isCurrentPlayer ? playerColor : AppTheme.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 4),

          // Counters
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildChip('$piecesAtHome', AppTheme.primaryGreen),
              const SizedBox(width: 4),
              _buildChip('$piecesOnBoard', playerColor.withValues(alpha: .7)),
            ],
          ),

          if (isCurrentPlayer && !isSpectator)
            Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: playerColor,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                player.isBot ? 'Bot' : 'Your Turn',
                style: const TextStyle(
                  fontSize: 8,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildChip(String text, Color bgColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 10,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getPlayerColor(PlayerColor color) {
    switch (color) {
      case PlayerColor.red:
        return AppTheme.primaryRed;
      case PlayerColor.blue:
        return AppTheme.primaryBlue;
      case PlayerColor.green:
        return AppTheme.primaryGreen;
      case PlayerColor.yellow:
        return AppTheme.primaryYellow;
    }
  }
}
