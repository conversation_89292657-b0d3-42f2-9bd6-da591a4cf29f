import 'package:flutter_test/flutter_test.dart';
import 'package:ludo_app/game/bot_ai.dart';
import 'package:ludo_app/game/game_engine.dart';
import 'package:ludo_app/models/game_state_model.dart';
import 'package:ludo_app/models/game_participant_model.dart';

void main() {
  group('Bot AI Tests', () {
    late GameStateModel gameState;
    late Player humanPlayer;
    late Player botPlayer;

    setUp(() {
      humanPlayer = Player(
        id: 'human_player',
        name: 'Human Player',
        color: PlayerColor.red,
      );
      botPlayer = Player(
        id: 'bot_player',
        name: 'Bot Player',
        color: PlayerColor.blue,
        isBot: true,
      );

      gameState = GameStateModel(
        gameId: 'test_game',
        mode: GameMode.local,
        players: [humanPlayer, botPlayer],
        gameStarted: true,
        currentPlayerIndex: 1, // Bot's turn
      );
    });

    group('Bot Decision Making', () {
      test('should prioritize moving pieces out with a 6', () {
        // All bot pieces are in starting area
        final testGameState = gameState.copyWith(
          players: [humanPlayer, botPlayer],
        );

        final pieceId = BotAI.makeBotMove(
          testGameState,
          6,
          BotDifficulty.medium,
        );
        expect(pieceId, isNotNull);

        // Should select a piece that can move out
        final selectedPiece = botPlayer.pieces.firstWhere(
          (p) => p.id == pieceId,
        );
        expect(selectedPiece.position, equals(-1)); // In starting area
      });

      test('should prioritize capturing opponent pieces', () {
        // Setup scenario where bot can capture
        final humanPiece = humanPlayer.pieces[0].copyWith(position: 15);
        final updatedHumanPlayer = humanPlayer.copyWith(
          pieces: [humanPiece, ...humanPlayer.pieces.skip(1)],
        );

        final botPiece = botPlayer.pieces[0].copyWith(position: 13);
        final updatedBotPlayer = botPlayer.copyWith(
          pieces: [botPiece, ...botPlayer.pieces.skip(1)],
        );

        final testGameState = gameState.copyWith(
          players: [updatedHumanPlayer, updatedBotPlayer],
        );

        final pieceId = BotAI.makeBotMove(
          testGameState,
          2,
          BotDifficulty.medium,
        );
        expect(
          pieceId,
          equals(botPiece.id),
        ); // Should choose the capturing piece
      });

      test('should avoid moving into opponent pieces on safe positions', () {
        // Setup scenario where opponent is on safe position
        final humanPiece = humanPlayer.pieces[0].copyWith(
          position: 1,
        ); // Safe position
        final updatedHumanPlayer = humanPlayer.copyWith(
          pieces: [humanPiece, ...humanPlayer.pieces.skip(1)],
        );

        final botPiece1 = botPlayer.pieces[0].copyWith(position: 0);
        final botPiece2 = botPlayer.pieces[1].copyWith(position: 5);
        final updatedBotPlayer = botPlayer.copyWith(
          pieces: [botPiece1, botPiece2, ...botPlayer.pieces.skip(2)],
        );

        final testGameState = gameState.copyWith(
          players: [updatedHumanPlayer, updatedBotPlayer],
        );

        final pieceId = BotAI.makeBotMove(
          testGameState,
          1,
          BotDifficulty.medium,
        );
        // Should choose piece 2 instead of piece 1 (which would land on safe position)
        expect(pieceId, equals(botPiece2.id));
      });

      test('should prioritize pieces closer to home', () {
        // Setup scenario with multiple movable pieces
        final botPiece1 = botPlayer.pieces[0].copyWith(position: 5);
        final botPiece2 = botPlayer.pieces[1].copyWith(
          position: 45,
        ); // Closer to home
        final updatedBotPlayer = botPlayer.copyWith(
          pieces: [botPiece1, botPiece2, ...botPlayer.pieces.skip(2)],
        );

        final testGameState = gameState.copyWith(
          players: [humanPlayer, updatedBotPlayer],
        );

        final pieceId = BotAI.makeBotMove(testGameState, 3, BotDifficulty.hard);
        expect(
          pieceId,
          equals(botPiece2.id),
        ); // Should choose piece closer to home
      });

      test('should return null when no valid moves available', () {
        // Setup scenario where no pieces can move
        final botPiece1 = botPlayer.pieces[0].copyWith(
          position: -6,
          hasReachedEnd: true,
        ); // Finished
        final botPiece2 = botPlayer.pieces[1].copyWith(
          position: -6,
          hasReachedEnd: true,
        ); // Finished
        final botPiece3 = botPlayer.pieces[2].copyWith(
          position: -6,
          hasReachedEnd: true,
        ); // Finished
        final botPiece4 = botPlayer.pieces[3].copyWith(
          position: -4,
        ); // In home, can't move 3 steps

        final updatedBotPlayer = botPlayer.copyWith(
          pieces: [botPiece1, botPiece2, botPiece3, botPiece4],
        );

        final testGameState = gameState.copyWith(
          players: [humanPlayer, updatedBotPlayer],
        );

        // Try to move 3 steps (would overshoot home for the only movable piece)
        final pieceId = BotAI.makeBotMove(
          testGameState,
          3,
          BotDifficulty.medium,
        );
        expect(pieceId, isNull);
      });
    });

    group('Bot Difficulty Levels', () {
      test('easy bot should make random valid moves', () {
        final botPiece1 = botPlayer.pieces[0].copyWith(position: 5);
        final botPiece2 = botPlayer.pieces[1].copyWith(position: 10);
        final updatedBotPlayer = botPlayer.copyWith(
          pieces: [botPiece1, botPiece2, ...botPlayer.pieces.skip(2)],
        );

        final testGameState = gameState.copyWith(
          players: [humanPlayer, updatedBotPlayer],
        );

        final pieceId = BotAI.makeBotMove(testGameState, 2, BotDifficulty.easy);
        expect(pieceId, isNotNull);
        expect([botPiece1.id, botPiece2.id].contains(pieceId), true);
      });

      test('medium bot should make strategic moves', () {
        // Setup capture scenario
        final humanPiece = humanPlayer.pieces[0].copyWith(position: 15);
        final updatedHumanPlayer = humanPlayer.copyWith(
          pieces: [humanPiece, ...humanPlayer.pieces.skip(1)],
        );

        final botPiece1 = botPlayer.pieces[0].copyWith(
          position: 13,
        ); // Can capture
        final botPiece2 = botPlayer.pieces[1].copyWith(
          position: 5,
        ); // Normal move
        final updatedBotPlayer = botPlayer.copyWith(
          pieces: [botPiece1, botPiece2, ...botPlayer.pieces.skip(2)],
        );

        final testGameState = gameState.copyWith(
          players: [updatedHumanPlayer, updatedBotPlayer],
        );

        final pieceId = BotAI.makeBotMove(
          testGameState,
          2,
          BotDifficulty.medium,
        );
        expect(pieceId, equals(botPiece1.id)); // Should choose capturing move
      });

      test('hard bot should make optimal moves', () {
        // Setup scenario where hard bot should choose piece closer to home
        final botPiece1 = botPlayer.pieces[0].copyWith(position: 5);
        final botPiece2 = botPlayer.pieces[1].copyWith(
          position: 45,
        ); // Much closer to home
        final updatedBotPlayer = botPlayer.copyWith(
          pieces: [botPiece1, botPiece2, ...botPlayer.pieces.skip(2)],
        );

        final testGameState = gameState.copyWith(
          players: [humanPlayer, updatedBotPlayer],
        );

        final pieceId = BotAI.makeBotMove(testGameState, 3, BotDifficulty.hard);
        expect(
          pieceId,
          equals(botPiece2.id),
        ); // Should choose piece closer to home
      });
    });

    group('Bot Move Validation', () {
      test('bot should only select valid moves', () {
        // Test multiple scenarios to ensure bot never makes invalid moves
        for (int diceValue = 1; diceValue <= 6; diceValue++) {
          final pieceId = BotAI.makeBotMove(
            gameState,
            diceValue,
            BotDifficulty.medium,
          );

          if (pieceId != null) {
            final piece = botPlayer.pieces.firstWhere((p) => p.id == pieceId);
            expect(GameEngine.canPieceMove(piece, diceValue, gameState), true);
          }
        }
      });

      test('bot should handle edge cases gracefully', () {
        // Test with pieces in various positions
        final botPiece1 = botPlayer.pieces[0].copyWith(
          position: -6,
          hasReachedEnd: true,
        );
        final botPiece2 = botPlayer.pieces[1].copyWith(position: -5);
        final botPiece3 = botPlayer.pieces[2].copyWith(position: 50);
        final botPiece4 = botPlayer.pieces[3].copyWith(position: -1);

        final updatedBotPlayer = botPlayer.copyWith(
          pieces: [botPiece1, botPiece2, botPiece3, botPiece4],
        );

        final testGameState = gameState.copyWith(
          players: [humanPlayer, updatedBotPlayer],
        );

        // Test various dice values
        for (int diceValue = 1; diceValue <= 6; diceValue++) {
          final pieceId = BotAI.makeBotMove(
            testGameState,
            diceValue,
            BotDifficulty.medium,
          );

          if (pieceId != null) {
            final piece = updatedBotPlayer.pieces.firstWhere(
              (p) => p.id == pieceId,
            );
            expect(
              GameEngine.canPieceMove(piece, diceValue, testGameState),
              true,
            );
          }
        }
      });
    });
  });
}
