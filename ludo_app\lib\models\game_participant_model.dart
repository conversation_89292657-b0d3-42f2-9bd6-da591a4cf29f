enum PlayerColor { red, blue, green, yellow }

class GameParticipantModel {
  final String id;
  final String gameId;
  final String userId;
  final int? position;
  final int score;
  final bool isWinner;
  final DateTime joinedAt;
  final DateTime? leftAt;
  final PlayerColor? playerColor;

  GameParticipantModel({
    required this.id,
    required this.gameId,
    required this.userId,
    this.position,
    required this.score,
    required this.isWinner,
    required this.joinedAt,
    this.leftAt,
    this.playerColor,
  });

  factory GameParticipantModel.fromJson(Map<String, dynamic> json) {
    return GameParticipantModel(
      id: json['id'] as String,
      gameId: json['game_id'] as String,
      userId: json['user_id'] as String,
      position: json['position'] as int?,
      score: json['score'] as int? ?? 0,
      isWinner: json['is_winner'] as bool? ?? false,
      joinedAt: DateTime.parse(json['joined_at'] as String),
      leftAt: json['left_at'] != null 
          ? DateTime.parse(json['left_at'] as String) 
          : null,
      playerColor: json['player_color'] != null 
          ? _parsePlayerColor(json['player_color'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'game_id': gameId,
      'user_id': userId,
      'position': position,
      'score': score,
      'is_winner': isWinner,
      'joined_at': joinedAt.toIso8601String(),
      'left_at': leftAt?.toIso8601String(),
      'player_color': playerColor?.name,
    };
  }

  static PlayerColor _parsePlayerColor(String color) {
    switch (color.toLowerCase()) {
      case 'red':
        return PlayerColor.red;
      case 'blue':
        return PlayerColor.blue;
      case 'green':
        return PlayerColor.green;
      case 'yellow':
        return PlayerColor.yellow;
      default:
        return PlayerColor.red;
    }
  }

  GameParticipantModel copyWith({
    String? id,
    String? gameId,
    String? userId,
    int? position,
    int? score,
    bool? isWinner,
    DateTime? joinedAt,
    DateTime? leftAt,
    PlayerColor? playerColor,
  }) {
    return GameParticipantModel(
      id: id ?? this.id,
      gameId: gameId ?? this.gameId,
      userId: userId ?? this.userId,
      position: position ?? this.position,
      score: score ?? this.score,
      isWinner: isWinner ?? this.isWinner,
      joinedAt: joinedAt ?? this.joinedAt,
      leftAt: leftAt ?? this.leftAt,
      playerColor: playerColor ?? this.playerColor,
    );
  }
}
